"""
Example Image Provider for demonstration purposes.
Returns test images instead of calling external APIs.
"""

import os
import base64
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..base import ImageProvider
from ..config import ProviderConfig
from ...schemas import ProviderMediaRequest, ProviderMediaResult


class ExampleImageProvider(ImageProvider):
    """Example image provider that returns test images."""

    def __init__(self):
        super().__init__()

    @property
    def provider_name(self) -> str:
        return "example_image"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize example provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate example images."""
        try:
            # Read test images from local directory
            test_images = []
            local_dir = Path(__file__).parent
            for i in range(min(request.num_images, 2)):  # Limit to available test images
                image_file = local_dir / f"banana_image_{i}.png"
                if image_file.exists():
                    # Read image file and encode as base64
                    with open(image_file, "rb") as f:
                        image_data = f.read()
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                    data_url = f"data:image/png;base64,{base64_data}"

                    test_images.append({
                        "image_url": data_url,
                        "thumbnail_url": data_url,
                        "width": 1024,
                        "height": 1024,
                        "style": "example_style",
                        "variant_name": f"example_variant_{i}",
                        "prompt_used": f"Example prompt for {request.product_title}",
                        "generation_metadata": {
                            "provider": "example_image",
                            "model": "example_model",
                            "style_type": "example",
                            "target_audience": ["example"],
                            "usage_context": ["example"]
                        }
                    })

            return ProviderMediaResult(
                success=True,
                provider_job_id="example_image_job_123",
                images=test_images,
                estimated_completion_time=5,
                quality_score=0.9
            )

        except Exception as e:
            return ProviderMediaResult(
                success=False,
                error_message=f"Example image generation failed: {str(e)}"
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download example media."""
        if media_url.startswith("data:"):
            header, encoded = media_url.split(",", 1)
            return base64.b64decode(encoded)
        else:
            # For file paths
            with open(media_url, "rb") as f:
                return f.read()