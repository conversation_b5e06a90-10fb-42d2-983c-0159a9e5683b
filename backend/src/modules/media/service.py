"""
Media Generation Service - Core business logic for ProductVideo
Generic service that uses provider plugins for different AI services.
"""

import logging
from enum import Enum
from typing import List, Optional, Dict, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from core.services.base_service import BaseService
from core.config import get_settings
from .models import (
    MediaJob, MediaVariant, Template, MediaJobStatus, MediaVariantStatus
)
from .schemas import MediaGenerateRequest, ProviderMediaRequest, ProviderMediaResult

from modules.queue.queue_service import celery_service as queue_service, TaskPriority
from modules.storage.storage_service import media_storage_service
from modules.templates.template_service import template_service
from .providers.manager import (
    get_provider,
    get_available_providers,
    config,
    ProviderConfig,
    # Legacy imports for backward compatibility
    provider_registry
)
from .engines.context_engine import context_engine
from .engines.prompt_engine import prompt_engine
from .common.error_handling import media_error_handler
from .common.rate_limiter import MediaRate<PERSON>im<PERSON>, DEFAULT_RATE_LIMITS
from .common.cache_manager import media_cache_manager
from .common.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)
settings = get_settings()




class MediaGenerationService(BaseService[MediaJob, dict, dict]):
    """Service for media generation operations using provider plugins."""

    def __init__(self):
        super().__init__(MediaJob)

    async def generate_media_with_provider(
        self,
        provider_name: str,
        request: ProviderMediaRequest
    ) -> ProviderMediaResult:
        """Generate media using a specific provider plugin. No fallbacks."""
        logger.info(f"generate_media_with_provider called with provider_name: {provider_name}")

        # Try the specified provider (override checking is now handled in the manager)
        logger.info(f"Calling _try_provider with: {provider_name}")
        result = await self._try_provider(provider_name, request)

        if not result.success:
            logger.warning(f"Provider {provider_name} failed: {result.error_message}")

        return result

    async def _try_provider(self, provider_name: str, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Try to generate media with a specific provider."""
        try:
            # Provider override checking is now handled in the manager
            resolved_provider_name = provider_name

            logger.info(f"Getting provider: {resolved_provider_name} for media type: {request.media_type}")

            # Use the new simple interface to get provider
            provider = await get_provider(resolved_provider_name, request.media_type)

            # Update resolved_provider_name to the actual provider name (may be overridden)
            actual_provider_name = provider.provider_name
            if actual_provider_name != resolved_provider_name:
                logger.info(f"Provider {resolved_provider_name} was resolved to {actual_provider_name} due to override")

            logger.info(f"Successfully got provider {actual_provider_name}: {type(provider)}")

            # Check if provider supports the requested media type
            if request.media_type not in provider.supported_media_types:
                logger.warning(f"Provider {actual_provider_name} does not support {request.media_type}")
                return ProviderMediaResult(
                    success=False,
                    error_message=f"Provider {actual_provider_name} does not support {request.media_type}"
                )

            logger.info(f"Calling generate_media on provider {actual_provider_name}")
            result = await provider.generate_media(request)

            if not result.success:
                logger.warning(f"Provider {actual_provider_name} failed: {result.error_message}")

            return result

        except Exception as e:
            logger.error(f"Error with provider {actual_provider_name if 'actual_provider_name' in locals() else resolved_provider_name}: {e}")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            raise

    async def get_available_providers(self, media_type: Optional[str] = None) -> List[str]:
        """Get list of available providers, optionally filtered by media type."""
        providers_dict = get_available_providers(media_type)

        if media_type:
            return providers_dict.get(media_type, [])
        else:
            # Return all providers as a flat list
            all_providers = []
            for provider_list in providers_dict.values():
                all_providers.extend(provider_list)
            return all_providers

    def _convert_to_internal_request(self, api_request: MediaGenerateRequest, product_id: str = None) -> ProviderMediaRequest:
        """Convert API request to internal provider request format."""
        # Extract product title from request
        product_title = product_id or "unknown_product"

        # If items are provided, find the matching product
        if api_request.items:
            for item in api_request.items:
                if str(item.product_id) == str(product_id):
                    product_title = item.prompt.split()[0] if item.prompt else str(item.product_id)
                    break

        return ProviderMediaRequest(
            product_title=product_title,
            media_type=api_request.media_type,
            template_id=api_request.template_id,
            voice_id=api_request.voice_id,
            text_input=api_request.text_input,
            custom_config={
                "aspect_ratio": api_request.aspect_ratio,
                "locale": api_request.locale,
                "mode": api_request.mode,
                "model": api_request.model,
                "settings": api_request.settings.dict() if api_request.settings else None,
                "items": api_request.items
            },
            num_images=4,  # Default variants count
            variants_count=4,
            aspect_ratio=api_request.aspect_ratio or "1:1",
            style="professional",
            model=api_request.model,
            settings=api_request.settings.dict() if api_request.settings else None
        )

    async def create_generation_jobs(
        self,
        db: AsyncSession,
        user_id: int,
        request: MediaGenerateRequest
    ) -> List[MediaJob]:
        """Create media generation jobs for multiple products."""
        jobs = []

        # Store the full request payload
        full_payload = {
            "media_type": request.media_type,
            "shop_id": request.shop_id,
            "product_ids": request.product_ids,
            "template_id": request.template_id,
            "voice_id": request.voice_id,
            "aspect_ratio": request.aspect_ratio,
            "locale": request.locale,
            "text_input": request.text_input,
            "mode": request.mode,
            "model": request.model,
            "settings": request.settings.dict() if request.settings else None,
            "items": [item.dict() if hasattr(item, 'dict') else item for item in (request.items or [])]
        }

        for item in request.items or []:
            product_id = item.product_id
            # Create main job
            job = MediaJob(
                user_id=user_id,
                product_id=int(product_id),
                status=MediaJobStatus.PENDING,
                media_type=request.media_type,
                provider=request.model or settings.VIDEO_PROVIDER,  # Use model if provided
                template_id=request.template_id,
                voice_id=request.voice_id,
                script=request.text_input, # Use text_input for voice generation
                custom_config={
                    "aspect_ratio": request.aspect_ratio,
                    "locale": request.locale,
                    "mode": request.mode,
                    "model": request.model,
                    "settings": request.settings.dict() if request.settings else None
                },
                full_payload=full_payload  # Store complete payload
            )

            db.add(job)
            await db.flush()  # Get the job ID

            # Create 4 variants per job (as per requirements)
            variants = [
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="square",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="vertical",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="horizontal",
                    status=MediaVariantStatus.GENERATING
                ),
                MediaVariant(
                    job_id=job.id,
                    user_id=user_id,
                    variant_name="story",
                    status=MediaVariantStatus.GENERATING
                )
            ]

            for variant in variants:
                db.add(variant)

            jobs.append(job)

        await db.commit()
        return jobs

    async def get_job_with_variants(self, db: AsyncSession, job_id: int) -> Optional[MediaJob]:
        """Get job with its variants."""
        from sqlalchemy.orm import selectinload

        result = await db.execute(
            select(MediaJob)
            .options(selectinload(MediaJob.variants))
            .where(MediaJob.id == job_id)
        )
        return result.scalar_one_or_none()

    async def process_generation_job(self, db: AsyncSession, job_id: int):
        """Process a media generation job using provider plugins (background task)."""
        logger.info(f"Processing media generation job {job_id}")

        # Get the job to extract details
        job = await self.get(db, job_id)
        if not job:
            raise ValueError(f"Job {job_id} not found")

        # Determine provider based on media type and settings
        # Extract model from the stored payload if available
        api_request = MediaGenerateRequest(**job.full_payload)
        provider_name = api_request.model

        # Convert stored payload back to API request format, then to internal format
        api_request = MediaGenerateRequest(**job.full_payload)
        request = self._convert_to_internal_request(api_request, job.product_id)

        # Generate media using provider
        result = await self.generate_media_with_provider(provider_name, request)

        if result.success:
            # Update job status and create variants
            await self._create_variants_from_result(db, job, result)
            job.status = MediaJobStatus.COMPLETED
            logger.info(f"Media generation job {job_id} completed successfully")
        else:
            job.status = MediaJobStatus.FAILED
            job.error_message = result.error_message
            logger.error(f"Media generation job {job_id} failed: {result.error_message}")

        await db.commit()



    async def _create_variants_from_result(
        self,
        db: AsyncSession,
        job: MediaJob,
        result: ProviderMediaResult
    ):
        """Create media variants from generation result."""
        # Update existing variants with results
        variants = (await db.execute(
            select(MediaVariant).filter(MediaVariant.job_id == job.id)
        )).scalars().all()

        if result.images:
            for i, image_data in enumerate(result.images):
                if i < len(variants):
                    variant = variants[i]
                    variant.image_url = image_data.get("image_url")
                    variant.thumbnail_url = image_data.get("thumbnail_url")
                    variant.status = MediaVariantStatus.READY

        elif result.variants:
            for i, variant_data in enumerate(result.variants):
                if i < len(variants):
                    variant = variants[i]
                    variant.video_url = variant_data.get("video_url")
                    variant.thumbnail_url = variant_data.get("thumbnail_url")
                    variant.status = MediaVariantStatus.READY

    async def regenerate_variant(
        self,
        job_id: int,
        variant_id: int,
        override_params: Optional[Dict[str, Any]] = None
    ):
        """Regenerate a specific media variant."""
        try:
            # Get the original job and variant
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.media.models import MediaJob, MediaVariant

            db = SessionLocal()

            job = db.execute(select(MediaJob).filter(MediaJob.id == job_id)).scalar_one_or_none()
            variant = db.execute(select(MediaVariant).filter(MediaVariant.id == variant_id)).scalar_one_or_none()

            if not job or not variant:
                raise ValueError("Job or variant not found")

            # Create new generation request based on original job
            request = ProviderMediaRequest(
                product_title=str(job.product_id),
                media_type=job.media_type,
                variants_count=1,
                aspect_ratio=job.custom_config.get("aspect_ratio", "1:1"),
                model=job.provider,
                settings=job.custom_config.get("settings", {}),
                custom_config=override_params or {}
            )

            # Generate new variant
            result = await self.generate_media_with_provider(job.provider, request)

            if result.success and result.variants:
                # Update the variant with new content
                new_variant_data = result.variants[0]
                variant.image_url = new_variant_data.get("image_url")
                variant.video_url = new_variant_data.get("video_url")
                variant.status = MediaVariantStatus.READY
                variant.updated_at = datetime.now()
                db.commit()

            db.close()
            logger.info(f"Successfully regenerated variant {variant_id} for job {job_id}")

        except Exception as e:
            logger.error(f"Failed to regenerate variant {variant_id}: {e}")
            raise

    async def push_to_platform(
        self,
        shop_id: int,
        product_id: str,
        variant_id: int,
        publish_options: Optional[Dict[str, Any]] = None
    ):
        """Push media variant to platform product media."""
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.media.models import MediaVariant
            from plugins.media_service import media_plugin_manager

            db = SessionLocal()

            # Get the variant
            variant = db.execute(select(MediaVariant).filter(MediaVariant.id == variant_id)).scalar_one_or_none()
            if not variant:
                raise ValueError("Variant not found")

            # Determine store type (default to Shopify for now)
            store_type = publish_options.get("store_type", "shopify") if publish_options else "shopify"

            # Get media URL
            media_url = variant.image_url or variant.video_url
            if not media_url:
                raise ValueError("No media URL available for variant")

            # Push to platform using plugin system
            result = await media_plugin_manager.push_media_to_product(
                store_type=store_type,
                shop_domain=publish_options.get("shop_domain", "") if publish_options else "",
                product_id=product_id,
                media_url=media_url,
                alt_text=publish_options.get("alt_text") if publish_options else None
            )

            db.close()
            logger.info(f"Successfully pushed variant {variant_id} to platform product {product_id}")
            return result

        except Exception as e:
            logger.error(f"Failed to push variant {variant_id} to platform: {e}")
            raise

    async def submit_for_review(
        self,
        variant_id: int,
        review_criteria: Optional[Dict[str, Any]] = None
    ):
        """Submit media variant for manual review."""
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.media.models import MediaVariant

            db = SessionLocal()

            # Get the variant
            variant = db.execute(select(MediaVariant).filter(MediaVariant.id == variant_id)).scalar_one_or_none()
            if not variant:
                raise ValueError("Variant not found")

            # Mark for manual review
            variant.needs_manual_review = True
            variant.qa_metadata = {
                "submitted_for_review_at": datetime.now().isoformat(),
                "review_criteria": review_criteria or {},
                "auto_quality_score": variant.quality_score
            }

            db.commit()
            db.close()

            logger.info(f"Submitted variant {variant_id} for manual review")

        except Exception as e:
            logger.error(f"Failed to submit variant {variant_id} for review: {e}")
            raise

    async def review_media(
        self,
        variant_id: int,
        reviewer_id: int,
        status: str,
        feedback: Optional[str] = None,
        quality_score: Optional[float] = None,
        rejection_reasons: Optional[List[str]] = None
    ):
        """Process manual review of media variant."""
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.media.models import MediaVariant, MediaReview, RejectedAsset

            db = SessionLocal()

            # Get the variant
            variant = db.execute(select(MediaVariant).filter(MediaVariant.id == variant_id)).scalar_one_or_none()
            if not variant:
                raise ValueError("Variant not found")

            # Create review record
            review = MediaReview(
                variant_id=variant_id,
                reviewer_id=reviewer_id,
                status=status,
                quality_score=quality_score,
                feedback=feedback,
                rejection_reasons=rejection_reasons
            )
            db.add(review)

            # Update variant based on review
            variant.needs_manual_review = False
            variant.quality_score = quality_score or variant.quality_score

            if status == "approved":
                variant.qa_metadata = {
                    **variant.qa_metadata,
                    "review_status": "approved",
                    "reviewed_at": datetime.now().isoformat(),
                    "reviewer_id": reviewer_id
                }
            elif status == "rejected":
                # Create rejected asset record for regeneration
                rejected_asset = RejectedAsset(
                    original_variant_id=variant_id,
                    job_id=variant.job_id,
                    user_id=variant.user_id,
                    rejection_reason=rejection_reasons[0] if rejection_reasons else "Quality issues",
                    rejection_category="manual_review",
                    rejection_details={
                        "feedback": feedback,
                        "rejection_reasons": rejection_reasons,
                        "quality_score": quality_score
                    },
                    original_media_url=variant.image_url or variant.video_url,
                    original_settings=variant.provider_metadata
                )
                db.add(rejected_asset)

                variant.qa_metadata = {
                    **variant.qa_metadata,
                    "review_status": "rejected",
                    "reviewed_at": datetime.now().isoformat(),
                    "reviewer_id": reviewer_id,
                    "rejection_reasons": rejection_reasons
                }

            db.commit()
            db.close()

            logger.info(f"Processed review for variant {variant_id}: {status}")

        except Exception as e:
            logger.error(f"Failed to process review for variant {variant_id}: {e}")
            raise

    async def get_pending_reviews(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get media variants pending manual review."""
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.media.models import MediaVariant

            db = SessionLocal()

            # Get variants needing review
            variants = db.execute(
                select(MediaVariant).filter(
                    MediaVariant.needs_manual_review == True
                ).limit(limit)
            ).scalars().all()

            result = []
            for variant in variants:
                result.append({
                    "variant_id": variant.id,
                    "job_id": variant.job_id,
                    "media_url": variant.image_url or variant.video_url,
                    "thumbnail_url": variant.thumbnail_url,
                    "alt_text": variant.alt_text,
                    "quality_score": variant.quality_score,
                    "qa_metadata": variant.qa_metadata,
                    "created_at": variant.created_at.isoformat()
                })

            db.close()
            return result

        except Exception as e:
            logger.error(f"Failed to get pending reviews: {e}")
            raise

    async def generate_accessibility_content(
        self,
        variant_id: int,
        media_url: str,
        media_type: str = "image"
    ):
        """Generate accessibility content (alt text, captions) for media."""
        try:
            from core.db.database import SessionLocal
            from sqlalchemy import select
            from modules.media.models import MediaVariant, MediaJob

            db = SessionLocal()

            # Get variant and job info
            variant = db.execute(select(MediaVariant).filter(MediaVariant.id == variant_id)).scalar_one_or_none()
            if not variant:
                raise ValueError("Variant not found")

            job = db.execute(select(MediaJob).filter(MediaJob.id == variant.job_id)).scalar_one_or_none()
            if not job:
                raise ValueError("Job not found")

            # Create product context from job data
            product_context = self._create_basic_product_context_from_job(job)

            if media_type == "image":
                # Generate alt text
                alt_text = await self._generate_alt_text_from_context(media_url, product_context)
                variant.alt_text = alt_text

            elif media_type == "video":
                # Generate captions
                captions = await self._generate_captions_from_context(media_url, product_context)
                variant.captions = captions

                # Also generate alt text for video thumbnail
                if variant.thumbnail_url:
                    alt_text = await self._generate_alt_text_from_context(variant.thumbnail_url, product_context)
                    variant.alt_text = alt_text

            db.commit()
            db.close()

            logger.info(f"Generated accessibility content for variant {variant_id}")

        except Exception as e:
            logger.error(f"Failed to generate accessibility content: {e}")
            raise

    def _create_basic_product_context_from_job(self, job) -> Dict[str, Any]:
        """Create basic product context from job data."""
        try:
            full_payload = job.full_payload or {}
            items = full_payload.get("items", [])

            if items:
                item = items[0]
                return {
                    "title": item.get("title", "Product"),
                    "category": item.get("category", "general"),
                    "colors": item.get("colors", []),
                    "materials": item.get("materials", []),
                    "key_features": item.get("key_features", []),
                    "target_audience": item.get("target_audience", "general_consumers")
                }
            else:
                return {
                    "title": str(job.product_id),
                    "category": "general",
                    "colors": [],
                    "materials": [],
                    "key_features": [],
                    "target_audience": "general_consumers"
                }

        except Exception as e:
            logger.warning(f"Failed to create product context: {e}")
            return {
                "title": "Product",
                "category": "general",
                "colors": [],
                "materials": [],
                "key_features": [],
                "target_audience": "general_consumers"
            }

    async def _generate_alt_text_from_context(self, media_url: str, product_context: Dict[str, Any]) -> str:
        """Generate alt text from product context."""
        try:
            alt_parts = []

            if product_context.get("title"):
                alt_parts.append(product_context["title"])

            if product_context.get("category") and product_context["category"] != "general":
                alt_parts.append(product_context["category"].replace("_", " "))

            if product_context.get("colors"):
                colors = product_context["colors"][:2]  # Limit to 2 colors
                alt_parts.append(f"in {', '.join(colors)}")

            if product_context.get("materials"):
                materials = product_context["materials"][:1]  # Limit to 1 material
                alt_parts.append(f"made of {materials[0]}")

            alt_text = " ".join(alt_parts)

            # Ensure it's not too long (accessibility guidelines recommend under 125 chars)
            if len(alt_text) > 125:
                alt_text = alt_text[:122] + "..."

            return alt_text or "Product image"

        except Exception as e:
            logger.warning(f"Failed to generate alt text: {e}")
            return "Product image"

    async def _generate_captions_from_context(self, media_url: str, product_context: Dict[str, Any]) -> str:
        """Generate captions from product context."""
        try:
            captions = []

            if product_context.get("title"):
                captions.append(f"Introducing {product_context['title']}")

            if product_context.get("key_features"):
                features = product_context["key_features"][:2]  # Limit to 2 features
                for feature in features:
                    captions.append(f"Features: {feature}")

            if product_context.get("target_audience") and product_context["target_audience"] != "general_consumers":
                audience = product_context["target_audience"].replace("_", " ")
                captions.append(f"Perfect for {audience}")

            captions.append("Shop now!")

            # Format as simple text captions (could be enhanced to SRT format)
            return " | ".join(captions)

        except Exception as e:
            logger.warning(f"Failed to generate captions: {e}")
            return ""

    async def generate_media_with_enhanced_pipeline(
        self,
        request: ProviderMediaRequest,
        user_id: int,
        tenant_id: int
    ) -> ProviderMediaResult:
        """
        Generate media using the enhanced pipeline with storage and template integration.

        This method provides the complete media generation workflow:
        1. Context analysis and enrichment
        2. Template selection and customization
        3. Professional prompt generation
        4. Multi-provider generation with fallbacks
        5. Quality assessment and optimization
        6. Storage and CDN distribution
        """
        try:
            # Step 1: Enhance product context using context engine
            if request.product_context:
                enhanced_context = await context_engine.analyze_product_context(
                    request.product_context,
                    request.shop_branding
                )
            else:
                enhanced_context = request.product_context

            # Step 2: Get template if specified
            template_config = None
            if request.template_id:
                template_config = await template_service.get_template_by_id(request.template_id)
                if template_config:
                    # Apply template customizations
                    request = self._apply_template_to_request(request, template_config)

            # Step 3: Generate professional prompts using prompt engine
            from .engines.prompt_engine import PromptContext, MediaType, Platform

            # Map media type
            media_type_map = {
                "image": MediaType.PRODUCT_PHOTOGRAPHY,
                "video": MediaType.PRODUCT_VIDEO,
                "text": MediaType.PRODUCT_DESCRIPTION
            }

            prompt_context = PromptContext(
                media_type=media_type_map.get(request.media_type, MediaType.PRODUCT_PHOTOGRAPHY),
                platform=Platform(request.target_platforms[0]) if request.target_platforms else None,
                aspect_ratio=request.aspect_ratio,
                style_preference=request.content_style,
                campaign_theme=request.campaign_theme,
                call_to_action=request.call_to_action
            )

            # Generate enhanced prompt
            generated_prompt = await prompt_engine.generate_prompt(
                product_context=enhanced_context,
                prompt_context=prompt_context,
                brand_context=self._create_brand_context_from_shop_branding(request.shop_branding)
            )

            # Update request with enhanced prompt
            if not request.custom_prompt:
                request.custom_prompt = generated_prompt.main_prompt

            # Step 4: Generate media using provider system
            if not request.model:
                raise ValueError(f"No provider specified for {request.media_type} generation. Please specify a 'model' in your request.")
            result = await self.generate_media_with_provider(request.model, request)

            # Step 5: Process and store generated media
            if result.success:
                result = await self._process_and_store_media(result, user_id, tenant_id, request)

            # Step 6: Apply quality enhancements
            if result.success and request.settings.get("enhance_quality", True):
                result = await self._apply_quality_enhancements(result, request)

            return result

        except Exception as e:
            logger.error(f"media generation failed: {e}")
            return ProviderMediaResult(
                success=False,
                error_message=f"Generation failed: {str(e)}"
            )

    async def _process_and_store_media(
        self,
        result: ProviderMediaResult,
        user_id: int,
        tenant_id: int,
        request: ProviderMediaRequest
    ) -> ProviderMediaResult:
        """Process and store generated media in the storage system."""
        try:
            stored_variants = []

            # Process each variant
            for variant in (result.variants or []):
                if request.media_type == "image" and "image_url" in variant:
                    # Download and store image
                    stored_variant = await self._store_image_variant(
                        variant, user_id, tenant_id, request
                    )
                elif request.media_type == "video" and "video_url" in variant:
                    # Download and store video
                    stored_variant = await self._store_video_variant(
                        variant, user_id, tenant_id, request
                    )
                elif request.media_type == "text":
                    # Store text content
                    stored_variant = await self._store_text_variant(
                        variant, user_id, tenant_id, request
                    )
                else:
                    stored_variant = variant

                stored_variants.append(stored_variant)

            # Update result with stored variants
            result.variants = stored_variants

            return result

        except Exception as e:
            logger.error(f"Failed to process and store media: {e}")
            result.error_message = f"Storage failed: {str(e)}"
            result.success = False
            return result

    async def _store_image_variant(
        self,
        variant: Dict[str, Any],
        user_id: int,
        tenant_id: int,
        request: ProviderMediaRequest
    ) -> Dict[str, Any]:
        """Store image variant in the storage system."""
        try:
            image_url = variant.get("image_url")
            if not image_url:
                return variant

            # Download image content
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(image_url)
                response.raise_for_status()
                image_content = response.content

            # Generate filename
            filename = f"{request.product_title}_{variant.get('variant_name', 'image')}.jpg"

            # Upload to storage
            media_file = await media_storage_service.upload_media(
                tenant_id=tenant_id,
                media_content=image_content,
                filename=filename,
                content_type="image/jpeg",
                metadata={
                    "user_id": user_id,
                    "product_title": request.product_title,
                    "variant_name": variant.get("variant_name"),
                    "generation_metadata": variant.get("generation_metadata", {})
                }
            )

            # Update variant with stored URLs
            variant.update({
                "stored_url": media_file.public_url,
                "storage_path": media_file.storage_path,
                "file_id": media_file.file_id
            })

            return variant

        except Exception as e:
            logger.error(f"Failed to store image variant: {e}")
            return variant

    async def _store_video_variant(
        self,
        variant: Dict[str, Any],
        user_id: int,
        tenant_id: int,
        request: ProviderMediaRequest
    ) -> Dict[str, Any]:
        """Store video variant in the storage system."""
        try:
            video_url = variant.get("video_url")
            if not video_url:
                return variant

            # Download video content
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(video_url)
                response.raise_for_status()
                video_content = response.content

            # Generate filename
            filename = f"{request.product_title}_{variant.get('variant_name', 'video')}.mp4"

            # Upload to storage
            media_file = await media_storage_service.upload_media(
                tenant_id=tenant_id,
                media_content=video_content,
                filename=filename,
                content_type="video/mp4",
                metadata={
                    "user_id": user_id,
                    "product_title": request.product_title,
                    "variant_name": variant.get("variant_name"),
                    "duration": variant.get("duration"),
                    "resolution": variant.get("resolution"),
                    "generation_metadata": variant.get("generation_metadata", {})
                }
            )

            # Update variant with stored URLs
            variant.update({
                "stored_url": media_file.public_url,
                "storage_path": media_file.storage_path,
                "file_id": media_file.file_id
            })

            return variant

        except Exception as e:
            logger.error(f"Failed to store video variant: {e}")
            return variant

    async def _store_text_variant(
        self,
        variant: Dict[str, Any],
        user_id: int,
        tenant_id: int,
        request: ProviderMediaRequest
    ) -> Dict[str, Any]:
        """Store text variant metadata."""
        # For text, we don't need to store files, just metadata
        variant.update({
            "stored": True,
            "user_id": user_id,
            "tenant_id": tenant_id,
            "product_title": request.product_title
        })
        return variant

    def _apply_template_to_request(
        self,
        request: ProviderMediaRequest,
        template_config: Dict[str, Any]
    ) -> ProviderMediaRequest:
        """Apply template configuration to the request."""
        # Apply template-specific settings
        if "default_aspect_ratio" in template_config:
            request.aspect_ratio = template_config["default_aspect_ratio"]

        if "recommended_style" in template_config:
            request.content_style = template_config["recommended_style"]

        # Apply template customizations from settings
        template_settings = request.settings.get("template_customizations", {})
        for key, value in template_settings.items():
            if key in template_config.get("customizable_elements", []):
                # Apply customization
                request.custom_config = request.custom_config or {}
                request.custom_config[f"template_{key}"] = value

        return request

    def _create_brand_context_from_shop_branding(
        self,
        shop_branding: Optional['ShopBranding']
    ) -> Optional['BrandContext']:
        """Create brand context from shop branding."""
        if not shop_branding:
            return None

        from .engines.context_engine import BrandContext

        return BrandContext(
            name=shop_branding.shop_name,
            industry="e-commerce",
            brand_voice=shop_branding.brand_voice or "professional",
            color_palette=shop_branding.color_palette or [],
            typography_style="modern",
            visual_style=shop_branding.visual_style,
            brand_values=shop_branding.brand_values or [],
            target_demographics=[],
            content_guidelines={}
        )

    async def _apply_quality_enhancements(
        self,
        result: ProviderMediaResult,
        request: ProviderMediaRequest
    ) -> ProviderMediaResult:
        """Apply quality enhancements to generated media."""
        # For now, just add quality metadata
        # In production, this could include upscaling, color correction, etc.

        for variant in (result.variants or []):
            variant["quality_enhanced"] = True
            variant["enhancement_applied"] = ["color_correction", "sharpening"]

        return result

    async def list_user_jobs(
        self,
        db: AsyncSession,
        user_id: int,
        page: int = 1,
        per_page: int = 20,
        status_filter: Optional[str] = None,
        product_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """List media generation jobs for a user with pagination and filters."""
        try:
            from sqlalchemy import select, func
            from modules.media.models import MediaJob, MediaVariant

            # Build query
            query = select(MediaJob).filter(MediaJob.user_id == user_id)

            # Apply filters
            if status_filter:
                try:
                    from modules.media.models import MediaJobStatus
                    status_enum = MediaJobStatus(status_filter)
                    query = query.filter(MediaJob.status == status_enum)
                except ValueError:
                    raise ValueError(f"Invalid status filter: {status_filter}")

            if product_id:
                query = query.filter(MediaJob.product_id == product_id)

            # Get total count
            total = (await db.execute(select(func.count()).select_from(query.subquery()))).scalar_one()

            # Apply pagination
            offset = (page - 1) * per_page
            jobs = (await db.execute(query.order_by(MediaJob.created_at.desc()).offset(offset).limit(per_page))).scalars().all()

            # Convert to response format
            job_responses = []
            for job in jobs:
                variants = (await db.execute(select(MediaVariant).filter(MediaVariant.job_id == job.id))).scalars().all()
                variant_responses = [{
                    "variant_id": variant.id,
                    "variant_name": variant.variant_name,
                    "status": variant.status.value,
                    "image_url": variant.image_url,
                    "video_url": variant.video_url,
                    "thumbnail_url": variant.thumbnail_url,
                    "duration": variant.duration_seconds
                } for variant in variants]

                job_response = {
                    "job_id": job.id,  # Use the internal DB ID directly
                    "product_id": job.product_id,
                    "status": job.status.value,
                    "media_type": job.media_type,
                    "created_at": job.created_at.isoformat() if job.created_at else None,
                    "variants": variant_responses
                }
                job_responses.append(job_response)

            return {
                "jobs": job_responses,
                "total": total,
                "page": page,
                "per_page": per_page
            }

        except Exception as e:
            logger.error(f"Failed to list user jobs: {e}")
            raise

    async def get_job_status(self, db: AsyncSession, job_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """Get detailed status of a specific job."""
        try:
            from sqlalchemy import select
            from modules.media.models import MediaJob, MediaVariant

            # Get job with ownership check
            job = await db.execute(
                select(MediaJob).filter(MediaJob.id == job_id, MediaJob.user_id == user_id)
            )
            job = job.scalar_one_or_none()

            if not job:
                return None

            # Get variants
            variants = await db.execute(
                select(MediaVariant).filter(MediaVariant.job_id == job_id)
            )
            variants = variants.scalars().all()

            # Use the job's progress_percentage field directly
            progress = job.progress_percentage or 0.0

            return {
                "job_id": job.id,
                "status": job.status.value,
                "progress": progress,
                "variants": [{
                    "variant_id": variant.id,
                    "variant_name": variant.variant_name,
                    "status": variant.status.value,
                    "image_url": variant.image_url,
                    "video_url": variant.video_url,
                    "thumbnail_url": variant.thumbnail_url,
                    "duration": variant.duration_seconds
                } for variant in variants]
            }

        except Exception as e:
            logger.error(f"Failed to get job status: {e}")
            raise

    async def cancel_job(self, db: AsyncSession, job_id: int, user_id: int) -> bool:
        """Cancel a media generation job."""
        try:
            from sqlalchemy import select, update
            from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus

            # Get job with ownership check
            job = await db.execute(
                select(MediaJob).filter(MediaJob.id == job_id, MediaJob.user_id == user_id)
            )
            job = job.scalar_one_or_none()

            if not job:
                return False

            # Only cancel if job is in pending or processing state
            if job.status not in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING]:
                return False

            # Update job status
            job.status = MediaJobStatus.CANCELLED

            # Update all variants to cancelled
            await db.execute(
                update(MediaVariant)
                .filter(MediaVariant.job_id == job_id)
                .values(status=MediaVariantStatus.CANCELLED)
            )

            await db.commit()
            logger.info(f"Cancelled job {job_id} for user {user_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to cancel job {job_id}: {e}")
            await db.rollback()
            return False

    async def retry_job(self, db: AsyncSession, job_id: int, user_id: int) -> Optional[MediaJob]:
        """Retry a failed media generation job."""
        try:
            from sqlalchemy import select, update
            from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus

            # Get job with ownership check
            job = await db.execute(
                select(MediaJob).filter(MediaJob.id == job_id, MediaJob.user_id == user_id)
            )
            job = job.scalar_one_or_none()

            if not job:
                return None

            # Only retry if job is in failed or cancelled state
            if job.status not in [MediaJobStatus.FAILED, MediaJobStatus.CANCELLED]:
                return None

            # Reset job status
            job.status = MediaJobStatus.PENDING

            # Reset all variants to generating
            await db.execute(
                update(MediaVariant)
                .filter(MediaVariant.job_id == job_id)
                .values(status=MediaVariantStatus.GENERATING)
            )

            await db.commit()
            logger.info(f"Retrying job {job_id} for user {user_id}")
            return job

        except Exception as e:
            logger.error(f"Failed to retry job {job_id}: {e}")
            await db.rollback()
            return None

    async def push_media_to_platforms(
        self,
        db: AsyncSession,
        job_id: int,
        user_id: int,
        publish_targets: List[str],
        publish_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Push media variants to specified platforms."""
        try:
            from sqlalchemy import select
            from modules.media.models import MediaJob, MediaVariant
            from plugins.media_service import media_plugin_manager

            # Get job with ownership check
            job = await db.execute(
                select(MediaJob).filter(MediaJob.id == job_id, MediaJob.user_id == user_id)
            )
            job = job.scalar_one_or_none()

            if not job:
                raise ValueError("Job not found")

            # Get completed variants
            variants = await db.execute(
                select(MediaVariant).filter(
                    MediaVariant.job_id == job_id,
                    MediaVariant.status == MediaVariantStatus.READY
                )
            )
            variants = variants.scalars().all()

            if not variants:
                raise ValueError("No completed variants found for this job")

            results = {}

            for target in publish_targets:
                try:
                    # Get media URL from first variant (assuming all variants are similar)
                    variant = variants[0]
                    media_url = variant.image_url or variant.video_url

                    if not media_url:
                        results[target] = {
                            "success": False,
                            "error": "No media URL available"
                        }
                        continue

                    # Push to platform
                    push_result = await media_plugin_manager.push_media_to_product(
                        store_type=target,
                        shop_domain=publish_options.get("shop_domain", "") if publish_options else "",
                        product_id=str(job.product_id),
                        media_url=media_url,
                        alt_text=publish_options.get("alt_text") if publish_options else None
                    )

                    results[target] = {
                        "success": push_result.get("success", False),
                        "platform_id": push_result.get("platform_id"),
                        "url": push_result.get("url"),
                        "error": push_result.get("error")
                    }

                except Exception as e:
                    logger.error(f"Failed to push to {target}: {e}")
                    results[target] = {
                        "success": False,
                        "error": str(e)
                    }

            return {
                "success": any(r.get("success", False) for r in results.values()),
                "results": results
            }

        except Exception as e:
            logger.error(f"Failed to push media to platforms: {e}")
            raise

    async def update_job_progress(
        self,
        db: AsyncSession,
        job_id: int,
        progress: float,
        status: Optional[MediaJobStatus] = None
    ):
        """Update job progress and optionally status."""
        try:
            from sqlalchemy import select, update

            # Update job progress
            update_data = {"progress_percentage": progress}
            if status:
                update_data["status"] = status

            await db.execute(
                update(MediaJob)
                .filter(MediaJob.id == job_id)
                .values(**update_data)
            )

            await db.commit()
            logger.info(f"Updated job {job_id} progress to {progress}%")

        except Exception as e:
            logger.error(f"Failed to update job progress: {e}")
            await db.rollback()
            raise

    async def complete_job(
        self,
        db: AsyncSession,
        job_id: int,
        variants_data: List[Dict[str, Any]]
    ):
        """Complete a job and create variants."""
        try:
            from sqlalchemy import select, update
            from datetime import datetime

            # Get the job
            job = await db.execute(
                select(MediaJob).filter(MediaJob.id == job_id)
            )
            job = job.scalar_one_or_none()

            if not job:
                raise ValueError(f"Job {job_id} not found")

            # Update job status
            job.status = MediaJobStatus.COMPLETED
            job.completed_at = datetime.now()

            # Create variants
            for variant_data in variants_data:
                variant = MediaVariant(
                    job_id=job_id,
                    user_id=job.user_id,
                    variant_name=variant_data["variant_name"],
                    status=MediaVariantStatus.READY if variant_data.get("status") == "ready" else MediaVariantStatus.FAILED,
                    image_url=variant_data.get("image_url"),
                    video_url=variant_data.get("video_url")
                )
                db.add(variant)

            await db.commit()
            logger.info(f"Completed job {job_id} with {len(variants_data)} variants")

        except Exception as e:
            logger.error(f"Failed to complete job: {e}")
            await db.rollback()
            raise

    async def fail_job(
        self,
        db: AsyncSession,
        job_id: int,
        error_message: str
    ):
        """Mark a job as failed with error message."""
        try:
            from sqlalchemy import select, update

            # Update job status
            await db.execute(
                update(MediaJob)
                .filter(MediaJob.id == job_id)
                .values(
                    status=MediaJobStatus.FAILED,
                    error_message=error_message
                )
            )

            await db.commit()
            logger.info(f"Marked job {job_id} as failed: {error_message}")

        except Exception as e:
            logger.error(f"Failed to fail job: {e}")
            await db.rollback()
            raise







# Create service instance
media_service = MediaGenerationService()
