"""
Comprehensive tests for media module schemas.
Tests all Pydantic models, validation, serialization, and business logic.
"""


import pytest
from typing import Dict, Any
from pydantic import ValidationError

from modules.media.models import MediaJobStatus
from modules.media.schemas import (
    ProductCategory, TargetAudience, ContentStyle, UsageContext,
    GenerationSettings, ShopBranding, ProductContext, ProductItem,
    MediaGenerateRequest, ProviderMediaRequest, ProviderMediaResult,
    MediaJobInfo, MediaGenerateResponse, MediaVariantInfo,
    MediaJobStatusResponse, PublishOptions, MediaPushRequest,
    MediaPushResponse, ImageInput, MediaJobListResponse
)

class TestEnums:
    """Test enum schemas."""
    
    def test_product_category_enum(self):
        """Test ProductCategory enum values."""
        assert ProductCategory.FASHION_APPAREL == "fashion_apparel"
        assert ProductCategory.FOOTWEAR == "footwear"
        assert ProductCategory.ELECTRONICS == "electronics"
        assert ProductCategory.BEAUTY_COSMETICS == "beauty_cosmetics"
        assert ProductCategory.HOME_DECOR == "home_decor"
        assert ProductCategory.JEWELRY == "jewelry"
        assert ProductCategory.SPORTS_FITNESS == "sports_fitness"
        assert ProductCategory.LUXURY_GOODS == "luxury_goods"
        assert ProductCategory.CHILDREN_BABY == "children_baby"
    
    def test_target_audience_enum(self):
        """Test TargetAudience enum values."""
        assert TargetAudience.GEN_Z == "gen_z"
        assert TargetAudience.MILLENNIALS == "millennials"
        assert TargetAudience.GEN_X == "gen_x"
        assert TargetAudience.BABY_BOOMERS == "baby_boomers"
        assert TargetAudience.LUXURY_BUYERS == "luxury_buyers"
        assert TargetAudience.BUDGET_CONSCIOUS == "budget_conscious"
        assert TargetAudience.EARLY_ADOPTERS == "early_adopters"
        assert TargetAudience.PROFESSIONALS == "professionals"
    
    def test_content_style_enum(self):
        """Test ContentStyle enum values."""
        assert ContentStyle.MINIMALIST == "minimalist"
        assert ContentStyle.LUXURY == "luxury"
        assert ContentStyle.LIFESTYLE == "lifestyle"
        assert ContentStyle.PROFESSIONAL == "professional"
        assert ContentStyle.PLAYFUL == "playful"
        assert ContentStyle.VINTAGE == "vintage"
        assert ContentStyle.MODERN == "modern"
        assert ContentStyle.ARTISTIC == "artistic"
    
    def test_usage_context_enum(self):
        """Test UsageContext enum values."""
        assert UsageContext.ECOMMERCE_LISTING == "ecommerce_listing"
        assert UsageContext.SOCIAL_MEDIA == "social_media"
        assert UsageContext.ADVERTISING == "advertising"
        assert UsageContext.WEBSITE_BANNER == "website_banner"
        assert UsageContext.EMAIL_MARKETING == "email_marketing"
        assert UsageContext.PRINT_CATALOG == "print_catalog"


class TestGenerationSettings:
    """Test GenerationSettings schema."""
    
    def test_generation_settings_creation(self):
        """Test creating GenerationSettings."""
        settings = GenerationSettings(
            size="1024x1024",
            quality="Standard",
            aspect_ratio="1:1",
            style="professional",
            background="white",
            lighting="studio"
        )
        
        assert settings.size == "1024x1024"
        assert settings.quality == "Standard"
        assert settings.aspect_ratio == "1:1"
        assert settings.style == "professional"
        assert settings.background == "white"
        assert settings.lighting == "studio"
    
    def test_generation_settings_defaults(self):
        """Test GenerationSettings with defaults."""
        settings = GenerationSettings()
        
        assert settings.size == "1024x1024"
        assert settings.quality == "Standard"
        assert settings.aspect_ratio == "1:1"
    
    def test_generation_settings_validation(self):
        """Test GenerationSettings validation."""
        # Valid settings
        settings = GenerationSettings(
            size="512x512",
            quality="High",
            aspect_ratio="16:9"
        )
        assert settings.size == "512x512"
        
        # Invalid aspect ratio should still work (no strict validation in this test)
        settings = GenerationSettings(aspect_ratio="custom")
        assert settings.aspect_ratio == "custom"


class TestShopBranding:
    """Test ShopBranding schema."""
    
    def test_shop_branding_creation(self):
        """Test creating ShopBranding."""
        branding = ShopBranding(
            shop_name="Premium Electronics",
            brand_voice="professional",
            brand_values=["quality", "innovation", "reliability"],
            color_palette=["#FF0000", "#00FF00", "#0000FF"],
            logo_url="https://example.com/logo.png",
            style_guide="Modern and clean aesthetic"
        )
        
        assert branding.shop_name == "Premium Electronics"
        assert branding.brand_voice == "professional"
        assert len(branding.brand_values) == 3
        assert "quality" in branding.brand_values
        assert len(branding.color_palette) == 3
        assert branding.logo_url == "https://example.com/logo.png"
        assert branding.style_guide == "Modern and clean aesthetic"
    
    def test_shop_branding_optional_fields(self):
        """Test ShopBranding with optional fields."""
        branding = ShopBranding(
            shop_name="Simple Store",
            brand_voice="casual"
        )
        
        assert branding.shop_name == "Simple Store"
        assert branding.brand_voice == "casual"
        assert branding.brand_values is None
        assert branding.color_palette is None
        assert branding.logo_url is None
        assert branding.style_guide is None


class TestProductContext:
    """Test ProductContext schema."""
    
    def test_product_context_creation(self):
        """Test creating ProductContext."""
        context = ProductContext(
            title="Premium Wireless Headphones",
            description="High-quality wireless headphones with noise cancellation",
            category=ProductCategory.ELECTRONICS,
            price=199.99,
            currency="USD",
            brand="AudioTech",
            target_audience=[TargetAudience.PROFESSIONALS],
            key_features=["noise cancellation", "wireless", "long battery"],
            usage_context=[UsageContext.ECOMMERCE_LISTING],
            style_preferences=ContentStyle.PROFESSIONAL
        )
        
        assert context.title == "Premium Wireless Headphones"
        assert context.description == "High-quality wireless headphones with noise cancellation"
        assert context.category == ProductCategory.ELECTRONICS
        assert context.price == 199.99
        assert context.currency == "USD"
        assert context.brand == "AudioTech"
        assert context.target_audience == [TargetAudience.PROFESSIONALS]
        assert len(context.key_features) == 3
        assert "wireless" in context.key_features
        assert context.usage_context == [UsageContext.ECOMMERCE_LISTING]
        assert context.style_preferences == ContentStyle.PROFESSIONAL
    
    def test_product_context_required_fields(self):
        """Test ProductContext with only required fields."""
        context = ProductContext(
            title="Simple Product",
            description="A simple product description",
            category=ProductCategory.FASHION_APPAREL,
            price=29.99,
            currency="USD"
        )
        
        assert context.title == "Simple Product"
        assert context.description == "A simple product description"
        assert context.category == ProductCategory.FASHION_APPAREL
        assert context.price == 29.99
        assert context.currency == "USD"
    
    def test_product_context_validation_errors(self):
        """Test ProductContext validation errors."""
        # Invalid price (negative)
        with pytest.raises(ValidationError):
            ProductContext(
                title="Test Product",
                description="Test description",
                category=ProductCategory.ELECTRONICS,
                price=-10.0,  # Negative price
                currency="USD"
            )

        # Empty title
        with pytest.raises(ValidationError):
            ProductContext(
                title="",  # Empty title
                description="Test description",
                category=ProductCategory.ELECTRONICS,
                price=99.99,
                currency="USD"
            )


class TestProductItem:
    """Test ProductItem schema."""
    
    def test_product_item_creation(self):
        """Test creating ProductItem."""
        product_context = ProductContext(
            title="Test Product",
            description="Test description",
            category=ProductCategory.ELECTRONICS,
            price=99.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=123,
            product_context=product_context,
            quantity=2,
            prompt="Custom generation prompt"
        )

        assert item.product_id == 123
        assert item.product_context.title == "Test Product"
        assert item.quantity == 2
        assert item.prompt == "Custom generation prompt"
    
    def test_product_item_defaults(self):
        """Test ProductItem with defaults."""
        product_context = ProductContext(
            title="Test Product",
            description="Test description",
            category=ProductCategory.ELECTRONICS,
            price=99.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=123,
            product_context=product_context
        )
        
        assert item.product_id == 123
        assert item.quantity == 1  # Default quantity
        assert item.prompt is None


class TestMediaGenerateRequest:
    """Test MediaGenerateRequest schema."""
    
    def test_media_generate_request_creation(self):
        """Test creating MediaGenerateRequest."""
        product_context = ProductContext(
            title="Test Product",
            description="Test description",
            category=ProductCategory.ELECTRONICS,
            price=99.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=123,
            product_context=product_context
        )
        
        settings = GenerationSettings(
            size="1024x1024",
            quality="High"
        )
        
        request = MediaGenerateRequest(
            mode="image",
            media_type="image",
            model="banana",
            settings=settings,
            items=[item],
            shop_id=1,
            aspect_ratio="1:1",
            locale="en",
            product_ids=[123]
        )
        
        assert request.mode == "image"
        assert request.media_type == "image"
        assert request.model == "banana"
        assert request.settings.size == "1024x1024"
        assert len(request.items) == 1
        assert request.items[0].product_id == 123
        assert request.shop_id == 1
        assert request.aspect_ratio == "1:1"
        assert request.locale == "en"
        assert request.product_ids == [123]
    
    def test_media_generate_request_validation(self):
        """Test MediaGenerateRequest validation."""
        # Invalid mode
        with pytest.raises(ValidationError):
            MediaGenerateRequest(
                mode="invalid_mode",  # Invalid mode
                items=[],
                product_ids=[]
            )
    
    def test_media_generate_request_optional_fields(self):
        """Test MediaGenerateRequest with optional fields."""
        product_context = ProductContext(
            title="Test Product",
            description="Test description",
            category=ProductCategory.ELECTRONICS,
            price=99.99,
            currency="USD"
        )
        
        item = ProductItem(
            product_id=123,
            product_context=product_context
        )
        
        request = MediaGenerateRequest(
            mode="image",
            items=[item],
            product_ids=[123]
        )
        
        assert request.mode == "image"
        assert len(request.items) == 1
        assert request.media_type is None  # Optional
        assert request.model is None  # Optional
        assert request.settings is None  # Optional


class TestProviderMediaRequest:
    """Test ProviderMediaRequest schema."""
    
    def test_provider_media_request_creation(self):
        """Test creating ProviderMediaRequest."""
        product_context = ProductContext(
            title="Provider Test Product",
            description="Test description for provider",
            category=ProductCategory.ELECTRONICS,
            price=149.99,
            currency="USD"
        )
        
        request = ProviderMediaRequest(
            product_title="Provider Test Product",
            product_description="Test description for provider",
            product_context=product_context,
            media_type="image",
            aspect_ratio="16:9",
            style="professional",
            language="en",
            custom_prompt="Generate a professional product image"
        )
        
        assert request.product_title == "Provider Test Product"
        assert request.product_description == "Test description for provider"
        assert request.product_context.title == "Provider Test Product"
        assert request.media_type == "image"
        assert request.aspect_ratio == "16:9"
        assert request.style == "professional"
        assert request.language == "en"
        assert request.custom_prompt == "Generate a professional product image"


class TestProviderMediaResult:
    """Test ProviderMediaResult schema."""
    
    def test_provider_media_result_success(self):
        """Test successful ProviderMediaResult."""
        result = ProviderMediaResult(
            success=True,
            provider_job_id="provider_123",
            images=[
                {"image_url": "https://example.com/image1.jpg"},
                {"image_url": "https://example.com/image2.jpg"}
            ],
            estimated_completion_time=30,
            quality_score=88.5,
            metadata={"generation_time": 25.3, "model_version": "v2.1"}
        )
        
        assert result.success is True
        assert result.provider_job_id == "provider_123"
        assert len(result.images) == 2
        assert result.images[0]["image_url"] == "https://example.com/image1.jpg"
        assert result.estimated_completion_time == 30
        assert result.quality_score == 88.5
        assert result.metadata["generation_time"] == 25.3
        assert result.error_message is None
    
    def test_provider_media_result_failure(self):
        """Test failed ProviderMediaResult."""
        result = ProviderMediaResult(
            success=False,
            error_message="Provider API rate limit exceeded",
            error_code="RATE_LIMIT_EXCEEDED"
        )
        
        assert result.success is False
        assert result.error_message == "Provider API rate limit exceeded"
        assert result.error_code == "RATE_LIMIT_EXCEEDED"
        assert result.provider_job_id is None
        assert result.images is None
        assert result.videos is None
        assert result.texts is None
