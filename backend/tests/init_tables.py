#!/usr/bin/env python3
"""
<PERSON>ript to drop all tables in the test database.
"""
import asyncio
import sys
from pathlib import Path

# Add src/ to sys.path
sys.path.insert(0, str(Path(__file__).resolve().parent.parent / "backend" / "src"))

import os
os.environ["TESTING"] = "True"

from core.config import get_settings
from core.db.database import Base
from sqlalchemy.ext.asyncio import create_async_engine

async def drop_all_tables():
    settings = get_settings()
    engine = create_async_engine(settings.DATABASE_URL, echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    await engine.dispose()
    print("All tables in the test database have been initialized successfully.")

if __name__ == "__main__":
    asyncio.run(drop_all_tables())