"""
Example Video Provider for demonstration purposes.
Returns test videos instead of calling external APIs.
"""

import os
import base64
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..base import VideoProvider
from ..config import ProviderConfig
from ...schemas import ProviderMediaRequest, ProviderMediaResult


class ExampleVideoProvider(VideoProvider):
    """Example video provider that returns test videos."""

    def __init__(self):
        super().__init__()

    @property
    def provider_name(self) -> str:
        return "example_video"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize example provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate example videos."""
        try:
            # Read test video
            test_variants = []
            local_dir = Path(__file__).parent
            video_file = local_dir / "veo3_video_0_with_image.mp4"

            if video_file.exists():
                # For test purposes, we'll use a data URL or file path
                video_url = str(video_file)

                test_variants.append({
                    "variant_name": "example_video_variant_0",
                    "video_url": video_url,
                    "thumbnail_url": video_url.replace('.mp4', '_thumb.jpg'),  # Placeholder
                    "duration": 8.0,
                    "resolution": "1920x1080",
                    "aspect_ratio": "16:9",
                    "style_type": "example_showcase",
                    "prompt_used": f"Example video prompt for {request.product_title}",
                    "generation_metadata": {
                        "provider": "example_video",
                        "model": "example_model",
                        "style_type": "product_showcase",
                        "target_audience": ["example"],
                        "platform_optimized": "web"
                    }
                })

            return ProviderMediaResult(
                success=True,
                provider_job_id="example_video_job_789",
                variants=test_variants,
                estimated_completion_time=10,
                quality_score=0.88
            )

        except Exception as e:
            return ProviderMediaResult(
                success=False,
                error_message=f"Example video generation failed: {str(e)}"
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download example media."""
        if media_url.startswith("/") or media_url.startswith("./"):
            with open(media_url, "rb") as f:
                return f.read()
        else:
            # Mock download for URLs
            return b"example video content"

    def _get_resolution_for_aspect(self, aspect_ratio: str) -> str:
        """Get resolution for aspect ratio."""
        resolution_map = {
            "1:1": "1024x1024",
            "16:9": "1920x1080",
            "9:16": "1080x1920",
            "4:5": "1024x1280",
            "3:4": "1024x1365"
        }
        return resolution_map.get(aspect_ratio, "1920x1080")