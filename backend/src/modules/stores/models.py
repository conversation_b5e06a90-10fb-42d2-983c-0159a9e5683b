from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Date, DateTime, Float, <PERSON><PERSON>ey, Integer, String, Text, BigInteger
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from core.db.database import Base


class Store(Base):
    """Store model for e-commerce platform connections."""

    __tablename__ = "stores"

    id = Column(BigInteger, primary_key=True, index=True)
    platform = Column(String, nullable=False, default="shopify")
    api_key = Column(String)
    api_secret_key = Column(String)
    admin_access_token = Column(String)
    shop_domain = Column(String, nullable=True)
    shop_id = Column(String, nullable=True)
    shop_name = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    last_sync = Column(DateTime(timezone=True))


    # Airbyte integration fields
    airbyte_source_id = Column(String(255), nullable=True, index=True)
    airbyte_destination_id = Column(String(255), nullable=True)
    airbyte_connection_id = Column(String(255), nullable=True, index=True)

    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Foreign Keys
    owner_id = Column(BigInteger, ForeignKey("users.id"))
    tenant_id = Column(BigInteger, ForeignKey("tenants.id"), nullable=True)

    # Relationships
    owner = relationship("User", back_populates="stores")
    tenant = relationship("Tenant", back_populates="stores")
    products = relationship("Product", back_populates="store")
    customers = relationship("Customer", back_populates="store")
    orders = relationship("Order", back_populates="store")

    # Sync-related relationships
    webhook_events = relationship("WebhookEvent", back_populates="store")
    sync_jobs = relationship("SyncJob", back_populates="store")
    sync_checkpoints = relationship("SyncCheckpoint", back_populates="store")
    dead_letter_items = relationship("DeadLetterQueue", back_populates="store")


# Import sync models after Store class to avoid circular imports
from modules.sync.models import WebhookEvent, SyncJob, SyncCheckpoint, DeadLetterQueue


class Forecast(Base):
    """Sales forecast model."""
    
    __tablename__ = "forecasts"

    id = Column(BigInteger, primary_key=True, index=True)
    store_id = Column(BigInteger, ForeignKey("stores.id"))
    forecast_date = Column(DateTime(timezone=True), nullable=False)
    predicted_sales = Column(Float, nullable=False)
    confidence_lower = Column(Float, nullable=True)
    confidence_upper = Column(Float, nullable=True)
    forecast_period = Column(String, nullable=True)
    raw_forecast_data = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    store = relationship("Store")


class StoreSale(Base):
    """Store sales tracking model."""
    
    __tablename__ = "store_sales"

    id = Column(BigInteger, primary_key=True, index=True)
    store_id = Column(BigInteger, ForeignKey("stores.id"), nullable=False)
    product_id = Column(String, nullable=True)
    variant_id = Column(String, nullable=True)
    sale_date = Column(Date, nullable=False)
    quantity_sold = Column(Integer, nullable=False)
    revenue = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    store = relationship("Store")


class ProductPerformance(Base):
    """Product performance tracking model."""
    
    __tablename__ = "product_performance"

    id = Column(BigInteger, primary_key=True, index=True)
    product_id = Column(String, nullable=False, unique=True)
    store_id = Column(BigInteger, ForeignKey("stores.id"), nullable=False)
    total_sold = Column(Integer, default=0)
    total_revenue = Column(Float, default=0.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    store = relationship("Store")


class StoreAnalyticsSnapshot(Base):
    """Store analytics snapshot model."""
    
    __tablename__ = "store_analytics_snapshots"

    id = Column(BigInteger, primary_key=True, index=True)
    store_id = Column(BigInteger, ForeignKey("stores.id"), nullable=False)
    date = Column(Date, nullable=False, unique=True)
    total_sales = Column(Float, default=0.0)
    total_orders = Column(Integer, default=0)
    average_order_value = Column(Float, default=0.0)
    new_customers = Column(Integer, default=0)
    conversion_rate = Column(Float, default=0.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    store = relationship("Store")


class SyncProgress(Base):
    """Sync progress tracking model."""

    __tablename__ = "sync_progress"

    id = Column(BigInteger, primary_key=True, index=True)
    store_id = Column(BigInteger, ForeignKey("stores.id"), nullable=False)
    sync_type = Column(String, nullable=False)  # 'products', 'orders', 'customers'
    status = Column(String, nullable=False)  # 'running', 'completed', 'failed', 'paused'
    total_items = Column(Integer, default=0)
    processed_items = Column(Integer, default=0)
    current_batch = Column(Integer, default=0)
    total_batches = Column(Integer, default=0)
    last_update = Column(DateTime(timezone=True), onupdate=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    completed_at = Column(DateTime(timezone=True), nullable=True)
    error_message = Column(Text, nullable=True)

    # Relationships
    store = relationship("Store")

    def to_dict(self):
        return {
            "id": self.id,
            "store_id": self.store_id,
            "sync_type": self.sync_type,
            "status": self.status,
            "total_items": self.total_items,
            "processed_items": self.processed_items,
            "current_batch": self.current_batch,
            "total_batches": self.total_batches,
            "progress_percentage": (self.processed_items / self.total_items * 100) if self.total_items > 0 else 0,
            "last_update": self.last_update.isoformat() if self.last_update else None,
            "created_at": self.created_at.isoformat(),
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "error_message": self.error_message
        }


class Holiday(Base):
    """Holiday tracking model for forecasting."""

    __tablename__ = "holidays"

    id = Column(BigInteger, primary_key=True, index=True)
    holiday_date = Column(DateTime(timezone=True), nullable=False, unique=True)
    name = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
