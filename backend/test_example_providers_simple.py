#!/usr/bin/env python3
"""
Simple test script to verify example providers work correctly.
Run this to ensure the new example providers are functioning properly.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from modules.media.providers.config import ProviderConfig
from modules.media.providers.image import ExampleImageProvider
from modules.media.providers.text import ExampleTextProvider
from modules.media.providers.video import ExampleVideoProvider
from modules.media.schemas import ProviderMediaRequest


async def test_example_image_provider():
    """Test the ExampleImageProvider works correctly."""
    print("🧪 Testing ExampleImageProvider...")

    # Create provider configuration
    config = ProviderConfig.from_dict("example_image", {
        "type": "image",
        "api_key": "",
        "timeout": 30,
        "model": "example_model",
        "capabilities": {
            "supported_formats": ["image"],
            "supported_styles": ["example_style", "test_style"],
            "supported_categories": ["test", "demo"]
        },
        "limits": {
            "max_variants_per_request": 2,
            "requests_per_minute": 100,
            "requests_per_hour": 1000
        },
        "costs": {"cost_per_unit": 0.0, "currency": "USD"},
        "quality": {"quality_score": 0.9, "average_generation_time": 5},
        "features": ["Example image generation", "Test data provider", "No API key required"],
        "image_config": {
            "aspect_ratios": {
                "square": {"width": 1024, "height": 1024},
                "landscape": {"width": 1920, "height": 1080}
            },
            "generation_params": {
                "quality": "high",
                "style": "example",
                "negative_prompt": "",
                "guidance_scale": 1.0,
                "num_inference_steps": 1
            }
        },
        "metadata": {
            "provider_name": "Example Image Provider",
            "description": "Example provider for testing and demonstration purposes"
        }
    })

    # Create and initialize provider
    provider = ExampleImageProvider()
    success = await provider.initialize(config)
    assert success, "Provider initialization failed"

    # Test media generation
    request = ProviderMediaRequest(
        product_title="Test Wireless Headphones",
        num_images=2
    )

    result = await provider.generate_media(request)

    # Verify results
    assert result.success, f"Generation failed: {result.error_message}"
    assert len(result.images) == 2, f"Expected 2 images, got {len(result.images)}"
    assert result.provider_job_id == "example_image_job_123"

    # Verify image data
    for image in result.images:
        assert "image_url" in image
        assert "thumbnail_url" in image
        assert "width" in image
        assert "height" in image
        assert "style" in image
        assert "variant_name" in image
        assert "prompt_used" in image
        assert "generation_metadata" in image

        # Check that files exist (either as data URLs or file paths)
        if image["image_url"].startswith("data:"):
            assert "base64," in image["image_url"]
        elif image["image_url"].endswith(".png"):
            assert Path(image["image_url"]).exists()

    print("✅ ExampleImageProvider test passed!")


async def test_example_text_provider():
    """Test the ExampleTextProvider works correctly."""
    print("🧪 Testing ExampleTextProvider...")

    # Create provider configuration
    config = ProviderConfig.from_dict("example_text", {
        "type": "text",
        "api_key": "",
        "timeout": 30,
        "model": "example_model",
        "capabilities": {
            "supported_formats": ["text"],
            "content_types": ["product_description", "marketing_copy", "social_caption", "seo_snippet"],
            "supported_languages": ["en"]
        },
        "limits": {
            "max_variants_per_request": 4,
            "requests_per_minute": 100,
            "requests_per_hour": 1000,
            "token_limits": {
                "product_description": 300,
                "marketing_copy": 400,
                "social_caption": 100,
                "seo_snippet": 200,
                "default": 300
            }
        },
        "costs": {"cost_per_unit": 0.0, "currency": "USD"},
        "quality": {"quality_score": 0.95, "average_generation_time": 2},
        "features": ["Example text generation", "Test data provider", "No API key required"],
        "text_config": {
            "temperature_settings": {"default": 0.7},
            "generation_params": {
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 50,
                "max_tokens": 1000,
                "presence_penalty": 0.0,
                "frequency_penalty": 0.0,
                "stop_sequences": []
            },
            "estimated_completion_time_per_variant": 2
        },
        "metadata": {
            "provider_name": "Example Text Provider",
            "description": "Example provider for testing and demonstration purposes"
        }
    })

    # Create and initialize provider
    provider = ExampleTextProvider()
    success = await provider.initialize(config)
    assert success, "Provider initialization failed"

    # Test media generation
    request = ProviderMediaRequest(
        product_title="Test Leather Handbag",
        num_variants=2
    )

    result = await provider.generate_media(request)

    # Verify results
    assert result.success, f"Generation failed: {result.error_message}"
    assert len(result.variants) == 2, f"Expected 2 variants, got {len(result.variants)}"
    assert result.provider_job_id == "example_text_job_456"

    # Verify text data
    for variant in result.variants:
        assert "content_type" in variant
        assert "text" in variant
        assert "word_count" in variant
        assert "character_count" in variant
        assert "variant_name" in variant
        assert "language" in variant
        assert "prompt_used" in variant

        # Verify text content is not empty
        assert len(variant["text"]) > 0
        assert variant["word_count"] > 0
        assert variant["character_count"] > 0

    print("✅ ExampleTextProvider test passed!")


async def test_example_video_provider():
    """Test the ExampleVideoProvider works correctly."""
    print("🧪 Testing ExampleVideoProvider...")

    # Create provider configuration
    config = ProviderConfig.from_dict("example_video", {
        "type": "video",
        "api_key": "",
        "timeout": 60,
        "model": "example_model",
        "capabilities": {
            "supported_formats": ["video"],
            "supported_aspect_ratios": ["16:9", "1:1", "9:16"],
            "max_duration_seconds": 10,
            "supported_person_generation": []
        },
        "limits": {
            "max_variants_per_request": 1,
            "requests_per_minute": 50,
            "requests_per_hour": 500
        },
        "costs": {"cost_per_unit": 0.0, "currency": "USD"},
        "quality": {"quality_score": 0.88, "average_generation_time": 10},
        "features": ["Example video generation", "Test data provider", "No API key required"],
        "video_config": {
            "variant_aspect_ratios": {
                "square": "1:1",
                "vertical": "9:16",
                "horizontal": "16:9"
            },
            "aspect_resolutions": {
                "1:1": "1024x1024",
                "9:16": "1080x1920",
                "16:9": "1920x1080"
            },
            "generation_params": {
                "resolution": "1080p",
                "fps": 30,
                "duration_seconds": 8,
                "codec": "H.264",
                "bitrate": "high"
            }
        },
        "metadata": {
            "provider_name": "Example Video Provider",
            "description": "Example provider for testing and demonstration purposes"
        }
    })

    # Create and initialize provider
    provider = ExampleVideoProvider()
    success = await provider.initialize(config)
    assert success, "Provider initialization failed"

    # Test media generation
    request = ProviderMediaRequest(
        product_title="Test Running Shoes",
        num_videos=1
    )

    result = await provider.generate_media(request)

    # Verify results
    assert result.success, f"Generation failed: {result.error_message}"
    assert len(result.variants) == 1, f"Expected 1 variant, got {len(result.variants)}"
    assert result.provider_job_id == "example_video_job_789"

    # Verify video data
    variant = result.variants[0]
    assert "variant_name" in variant
    assert "video_url" in variant
    assert "thumbnail_url" in variant
    assert "duration" in variant
    assert "resolution" in variant
    assert "aspect_ratio" in variant
    assert "style_type" in variant
    assert "prompt_used" in variant
    assert "generation_metadata" in variant

    # Verify video file exists
    if variant["video_url"].endswith(".mp4"):
        assert Path(variant["video_url"]).exists(), f"Video file not found: {variant['video_url']}"

    # Verify expected values
    assert variant["duration"] == 8.0
    assert variant["resolution"] == "1920x1080"
    assert variant["aspect_ratio"] == "16:9"

    print("✅ ExampleVideoProvider test passed!")


async def main():
    """Run all tests."""
    print("🚀 Starting Example Providers Test Suite")
    print("=" * 50)

    try:
        await test_example_image_provider()
        await test_example_text_provider()
        await test_example_video_provider()

        print("\n" + "=" * 50)
        print("🎉 All example provider tests passed!")
        print("✅ Your E2E test configuration has been successfully updated!")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)