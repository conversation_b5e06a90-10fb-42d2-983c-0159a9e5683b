"""
Example of using the simplified provider system.
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from modules.media.providers import (
    get_text_provider,
    get_image_provider,
    get_video_provider,
    get_provider,
    get_available_providers
)
from modules.media.schemas import ProviderMediaRequest


async def main():
    """Demonstrate the simplified provider system."""
    
    print("=== Simplified Provider System Demo ===\n")
    
    # 1. Show available providers
    print("1. Available providers:")
    providers = get_available_providers()
    for media_type, provider_list in providers.items():
        print(f"   {media_type}: {provider_list}")
    print()
    
    # 2. Get specific providers by type
    print("2. Getting providers by type:")
    
    try:
        # Get text provider
        text_provider = await get_text_provider("example_text")
        print(f"   Text provider: {text_provider.provider_name}")
        
        # Get image provider  
        image_provider = await get_image_provider("example_image")
        print(f"   Image provider: {image_provider.provider_name}")
        
        # Get video provider
        video_provider = await get_video_provider("example_video")
        print(f"   Video provider: {video_provider.provider_name}")
        
    except Exception as e:
        print(f"   Error getting providers: {e}")
    print()
    
    # 3. Use generic get_provider function
    print("3. Using generic get_provider function:")
    
    try:
        # Get providers using generic interface
        text_provider = await get_provider("example_text", "text")
        print(f"   Text provider via generic: {text_provider.provider_name}")
        
        image_provider = await get_provider("example_image", "image")
        print(f"   Image provider via generic: {image_provider.provider_name}")
        
        video_provider = await get_provider("example_video", "video")
        print(f"   Video provider via generic: {video_provider.provider_name}")
        
    except Exception as e:
        print(f"   Error with generic interface: {e}")
    print()
    
    # 4. Test caching (second call should be instant)
    print("4. Testing provider caching:")
    
    try:
        import time
        
        # First call (should initialize)
        start = time.time()
        provider1 = await get_text_provider("example_text")
        first_time = time.time() - start
        
        # Second call (should use cache)
        start = time.time()
        provider2 = await get_text_provider("example_text")
        second_time = time.time() - start
        
        print(f"   First call time: {first_time:.4f}s")
        print(f"   Second call time: {second_time:.4f}s")
        print(f"   Same instance: {provider1 is provider2}")
        
    except Exception as e:
        print(f"   Error testing caching: {e}")
    print()
    
    # 5. Test error handling
    print("5. Testing error handling:")
    
    try:
        # Try invalid provider name
        await get_text_provider("invalid_provider")
    except ValueError as e:
        print(f"   Expected error for invalid provider: {e}")
    
    try:
        # Try invalid media type
        await get_provider("example_text", "invalid_type")
    except ValueError as e:
        print(f"   Expected error for invalid media type: {e}")
    print()
    
    # 6. Generate sample media (if providers work)
    print("6. Testing media generation:")
    
    try:
        # Create a simple request
        request = ProviderMediaRequest(
            media_type="text",
            product_title="Test Product",
            product_description="A simple test product",
            num_images=1
        )
        
        # Get text provider and generate content
        text_provider = await get_text_provider("example_text")
        result = await text_provider.generate_media(request)
        
        print(f"   Text generation success: {result.success}")
        if result.success and result.media_items:
            print(f"   Generated text preview: {result.media_items[0].content[:100]}...")
        elif result.error_message:
            print(f"   Error: {result.error_message}")
            
    except Exception as e:
        print(f"   Error testing media generation: {e}")
    print()
    
    print("=== Demo Complete ===")


if __name__ == "__main__":
    asyncio.run(main())
