"""
Stripe integration service for ProductVideo platform.
Handles subscriptions, metered billing, and webhook processing.
"""

import logging
import stripe
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from core.config import get_settings
from modules.auth.models import Tenant
from .models import Subscription, BillingUsage, Invoice, PaymentMethod, SubscriptionStatus, UsageType
from .schemas import (
    CreateSubscriptionRequest, CreateSubscriptionResponse,
    RecordUsageRequest, RecordUsageResponse,
    StripeWebhookEvent
)

logger = logging.getLogger(__name__)
settings = get_settings()

# Initialize Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class StripeService:
    """
    Service for Stripe integration.
    Handles subscriptions, usage tracking, and webhook processing.
    """
    
    def __init__(self):
        self.webhook_secret = settings.STRIPE_WEBHOOK_SECRET
        
        # Pricing configuration
        self.price_ids = {
            "starter": settings.STRIPE_STARTER_PRICE_ID,
            "professional": settings.STRIPE_PROFESSIONAL_PRICE_ID,
            "enterprise": settings.STRIPE_ENTERPRISE_PRICE_ID,
        }
        
        # Usage-based pricing
        self.usage_price_ids = {
            UsageType.VIDEO_GENERATION: settings.STRIPE_VIDEO_GENERATION_PRICE_ID,
            UsageType.STORAGE_GB: settings.STRIPE_STORAGE_PRICE_ID,
            UsageType.BANDWIDTH_GB: settings.STRIPE_BANDWIDTH_PRICE_ID,
        }

        # Credit package pricing
        self.credit_packages = {
            "credits_1000": {
                "credits": 1000,
                "price_id": getattr(settings, 'STRIPE_CREDITS_1000_PRICE_ID', None),
                "amount": 9.99,  # $9.99 for 1000 credits
            },
            "credits_5000": {
                "credits": 5000,
                "price_id": getattr(settings, 'STRIPE_CREDITS_5000_PRICE_ID', None),
                "amount": 39.99,  # $39.99 for 5000 credits
            },
            "credits_10000": {
                "credits": 10000,
                "price_id": getattr(settings, 'STRIPE_CREDITS_10000_PRICE_ID', None),
                "amount": 69.99,  # $69.99 for 10000 credits
            },
            "credits_25000": {
                "credits": 25000,
                "price_id": getattr(settings, 'STRIPE_CREDITS_25000_PRICE_ID', None),
                "amount": 149.99,  # $149.99 for 25000 credits
            },
        }
        
        logger.info("Initialized Stripe service")
    
    async def create_customer(self, tenant: Tenant) -> str:
        """
        Create a Stripe customer for a tenant.
        
        Args:
            tenant: Tenant to create customer for
            
        Returns:
            Stripe customer ID
        """
        try:
            customer = stripe.Customer.create(
                email=tenant.billing_email,
                name=tenant.name,
                metadata={
                    "tenant_id": str(tenant.id),
                    "shop_domain": tenant.shop_domain or "",
                }
            )
            
            logger.info(f"Created Stripe customer {customer.id} for tenant {tenant.id}")
            return customer.id
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe customer for tenant {tenant.id}: {e}")
            raise
    
    async def create_subscription(
        self, 
        db: AsyncSession,
        request: CreateSubscriptionRequest
    ) -> CreateSubscriptionResponse:
        """
        Create a subscription for a tenant.
        
        Args:
            db: Database session
            request: Subscription creation request
            
        Returns:
            Subscription creation response
        """
        try:
            # Get tenant
            result = await db.execute(select(Tenant).where(Tenant.id == request.tenant_id))
            tenant = result.scalar_one_or_none()
            if not tenant:
                raise ValueError(f"Tenant {request.tenant_id} not found")
            
            # Ensure customer exists
            if not tenant.stripe_customer_id:
                customer_id = await self.create_customer(tenant)
                tenant.stripe_customer_id = customer_id
                await db.commit()
            
            # Create subscription
            subscription_data = {
                "customer": tenant.stripe_customer_id,
                "items": [{"price": request.price_id}],
                "metadata": {
                    "tenant_id": str(tenant.id),
                },
                "expand": ["latest_invoice.payment_intent"],
            }
            
            if request.payment_method_id:
                subscription_data["default_payment_method"] = request.payment_method_id
            
            if request.trial_days:
                subscription_data["trial_period_days"] = request.trial_days
            
            stripe_subscription = stripe.Subscription.create(**subscription_data)
            
            # Save subscription to database
            subscription = Subscription(
                tenant_id=tenant.id,
                stripe_subscription_id=stripe_subscription.id,
                stripe_customer_id=tenant.stripe_customer_id,
                stripe_price_id=request.price_id,
                status=SubscriptionStatus(stripe_subscription.status),
                current_period_start=datetime.fromtimestamp(stripe_subscription.current_period_start),
                current_period_end=datetime.fromtimestamp(stripe_subscription.current_period_end),
                trial_start=datetime.fromtimestamp(stripe_subscription.trial_start) if stripe_subscription.trial_start else None,
                trial_end=datetime.fromtimestamp(stripe_subscription.trial_end) if stripe_subscription.trial_end else None,
                amount=Decimal(str(stripe_subscription.items.data[0].price.unit_amount / 100)),
                currency=stripe_subscription.items.data[0].price.currency,
                metadata=stripe_subscription.metadata,
            )
            
            db.add(subscription)
            await db.commit()
            await db.refresh(subscription)
            
            # Prepare response
            client_secret = None
            if stripe_subscription.latest_invoice and stripe_subscription.latest_invoice.payment_intent:
                client_secret = stripe_subscription.latest_invoice.payment_intent.client_secret
            
            logger.info(f"Created subscription {subscription.id} for tenant {tenant.id}")
            
            return CreateSubscriptionResponse(
                subscription=subscription,
                client_secret=client_secret,
                status=stripe_subscription.status
            )
            
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create subscription for tenant {request.tenant_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating subscription: {e}")
            raise
    
    async def record_usage(
        self,
        db: AsyncSession,
        request: RecordUsageRequest
    ) -> RecordUsageResponse:
        """
        Record usage for metered billing.
        
        Args:
            db: Database session
            request: Usage recording request
            
        Returns:
            Usage recording response
        """
        try:
            # Get tenant and subscription
            result = await db.execute(select(Tenant).where(Tenant.id == request.tenant_id))
            tenant = result.scalar_one_or_none()
            if not tenant:
                raise ValueError(f"Tenant {request.tenant_id} not found")
            
            # Get active subscription
            subscription_result = await db.execute(
                select(Subscription)
                .where(Subscription.tenant_id == request.tenant_id)
                .where(Subscription.status == SubscriptionStatus.ACTIVE)
                .order_by(Subscription.created_at.desc())
            )
            subscription = subscription_result.scalar_one_or_none()
            
            # Calculate billing period (current month)
            now = datetime.utcnow()
            period_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            if now.month == 12:
                period_end = period_start.replace(year=now.year + 1, month=1) - timedelta(seconds=1)
            else:
                period_end = period_start.replace(month=now.month + 1) - timedelta(seconds=1)
            
            # Create usage record
            usage_record = BillingUsage(
                tenant_id=request.tenant_id,
                usage_type=request.usage_type,
                quantity=request.quantity,
                billing_period_start=period_start,
                billing_period_end=period_end,
                resource_id=request.resource_id,
                resource_type=request.resource_type,
                metadata=request.metadata or {},
            )
            
            # Report to Stripe if subscription exists and has metered pricing
            stripe_usage_record_id = None
            if subscription and request.usage_type in self.usage_price_ids:
                try:
                    # Find subscription item for this usage type
                    stripe_subscription = stripe.Subscription.retrieve(subscription.stripe_subscription_id)
                    
                    for item in stripe_subscription.items.data:
                        if item.price.id == self.usage_price_ids[request.usage_type]:
                            # Report usage to Stripe
                            stripe_usage_record = stripe.UsageRecord.create(
                                subscription_item=item.id,
                                quantity=int(request.quantity),
                                timestamp=int(now.timestamp()),
                                action="increment"
                            )
                            stripe_usage_record_id = stripe_usage_record.id
                            usage_record.stripe_usage_record_id = stripe_usage_record_id
                            usage_record.stripe_subscription_item_id = item.id
                            break
                            
                except stripe.error.StripeError as e:
                    logger.warning(f"Failed to report usage to Stripe: {e}")
                    # Continue without Stripe reporting
            
            db.add(usage_record)
            await db.commit()
            await db.refresh(usage_record)
            
            logger.info(f"Recorded usage for tenant {request.tenant_id}: {request.usage_type} = {request.quantity}")
            
            return RecordUsageResponse(
                usage_record=usage_record,
                stripe_usage_record_id=stripe_usage_record_id
            )
            
        except Exception as e:
            logger.error(f"Failed to record usage: {e}")
            raise

    async def create_credit_purchase_session(
        self,
        db: AsyncSession,
        tenant_id: int,
        package_id: str,
        success_url: str,
        cancel_url: str
    ) -> Dict[str, Any]:
        """
        Create a Stripe checkout session for credit purchase.

        Args:
            db: Database session
            tenant_id: Tenant ID
            package_id: Credit package identifier
            success_url: Success redirect URL
            cancel_url: Cancel redirect URL

        Returns:
            Checkout session data
        """
        try:
            # Get tenant
            result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
            tenant = result.scalar_one_or_none()
            if not tenant:
                raise ValueError(f"Tenant {tenant_id} not found")

            # Validate package
            if package_id not in self.credit_packages:
                raise ValueError(f"Invalid credit package: {package_id}")

            package = self.credit_packages[package_id]

            # Ensure customer exists
            if not tenant.stripe_customer_id:
                customer_id = await self.create_customer(tenant)
                tenant.stripe_customer_id = customer_id
                await db.commit()

            # Create checkout session
            session_data = {
                "customer": tenant.stripe_customer_id,
                "payment_method_types": ["card"],
                "line_items": [{
                    "price": package["price_id"],
                    "quantity": 1,
                }],
                "mode": "payment",
                "success_url": success_url,
                "cancel_url": cancel_url,
                "metadata": {
                    "tenant_id": str(tenant_id),
                    "package_id": package_id,
                    "credits": str(package["credits"]),
                },
                "client_reference_id": f"credit_purchase_{tenant_id}_{package_id}",
            }

            session = stripe.checkout.Session.create(**session_data)

            logger.info(f"Created credit purchase session {session.id} for tenant {tenant_id}, package {package_id}")

            return {
                "session_id": session.id,
                "url": session.url,
                "package": package,
            }

        except stripe.error.StripeError as e:
            logger.error(f"Failed to create credit purchase session for tenant {tenant_id}: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error creating credit purchase session: {e}")
            raise
    
    async def process_webhook(
        self,
        db: AsyncSession,
        payload: bytes,
        signature: str
    ) -> Dict[str, Any]:
        """
        Process Stripe webhook event.
        
        Args:
            db: Database session
            payload: Raw webhook payload
            signature: Stripe signature header
            
        Returns:
            Processing result
        """
        try:
            # Verify webhook signature
            event = stripe.Webhook.construct_event(
                payload, signature, self.webhook_secret
            )
            
            logger.info(f"Processing Stripe webhook: {event['type']}")
            
            # Handle different event types
            if event["type"] == "customer.subscription.created":
                await self._handle_subscription_created(db, event["data"]["object"])
            elif event["type"] == "customer.subscription.updated":
                await self._handle_subscription_updated(db, event["data"]["object"])
            elif event["type"] == "customer.subscription.deleted":
                await self._handle_subscription_deleted(db, event["data"]["object"])
            elif event["type"] == "invoice.payment_succeeded":
                await self._handle_invoice_payment_succeeded(db, event["data"]["object"])
            elif event["type"] == "invoice.payment_failed":
                await self._handle_invoice_payment_failed(db, event["data"]["object"])
            elif event["type"] == "checkout.session.completed":
                await self._handle_checkout_session_completed(db, event["data"]["object"])
            elif event["type"] == "customer.created":
                await self._handle_customer_created(db, event["data"]["object"])
            else:
                logger.info(f"Unhandled webhook event type: {event['type']}")
            
            return {"status": "success", "event_type": event["type"]}
            
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid Stripe webhook signature: {e}")
            raise ValueError("Invalid webhook signature")
        except Exception as e:
            logger.error(f"Error processing Stripe webhook: {e}")
            raise
    
    async def _handle_subscription_created(self, db: AsyncSession, subscription_data: Dict):
        """Handle subscription.created webhook."""
        # Implementation for subscription creation
        pass
    
    async def _handle_subscription_updated(self, db: AsyncSession, subscription_data: Dict):
        """Handle subscription.updated webhook."""
        # Implementation for subscription updates
        pass
    
    async def _handle_subscription_deleted(self, db: AsyncSession, subscription_data: Dict):
        """Handle subscription.deleted webhook."""
        # Implementation for subscription deletion
        pass
    
    async def _handle_invoice_payment_succeeded(self, db: AsyncSession, invoice_data: Dict):
        """Handle invoice.payment_succeeded webhook."""
        # Implementation for successful payments
        pass
    
    async def _handle_invoice_payment_failed(self, db: AsyncSession, invoice_data: Dict):
        """Handle invoice.payment_failed webhook."""
        # Implementation for failed payments
        pass
    
    async def _handle_checkout_session_completed(self, db: AsyncSession, session_data: Dict):
        """Handle checkout.session.completed webhook for credit purchases."""
        try:
            # Check if this is a credit purchase session
            metadata = session_data.get("metadata", {})
            if not metadata.get("package_id") or not metadata.get("tenant_id"):
                logger.info("Checkout session is not a credit purchase, skipping")
                return

            tenant_id = int(metadata["tenant_id"])
            package_id = metadata["package_id"]
            credits_amount = int(metadata["credits"])

            # Get payment intent for the session
            payment_intent_id = session_data.get("payment_intent")
            if not payment_intent_id:
                logger.error(f"No payment intent found for credit purchase session {session_data['id']}")
                return

            # Verify payment was successful
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)
            if payment_intent.status != "succeeded":
                logger.warning(f"Payment not successful for session {session_data['id']}: {payment_intent.status}")
                return

            # Add credits to tenant
            from .credit_service import credit_service
            success = await credit_service.purchase_credits(
                tenant_id=tenant_id,
                credit_amount=credits_amount,
                stripe_payment_id=payment_intent_id,
                description=f"Credit purchase: {credits_amount} credits ({package_id})"
            )

            if success:
                logger.info(f"Successfully processed credit purchase for tenant {tenant_id}: {credits_amount} credits")
            else:
                logger.error(f"Failed to add credits for tenant {tenant_id} after successful payment")

        except Exception as e:
            logger.error(f"Error processing checkout session completed: {e}")
            raise

    async def _handle_customer_created(self, db: AsyncSession, customer_data: Dict):
        """Handle customer.created webhook."""
        # Implementation for customer creation
        pass


# Create service instance
stripe_service = StripeService()
