from typing import Any, As<PERSON><PERSON><PERSON>ator, <PERSON><PERSON>

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine

from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from core.config import get_settings
from core.db.base_declarative import Base # Import Base from the new file


def get_db_components(database_url: str, echo: bool = False) -> Tuple[Any, Any]:
    """
    Returns a SQLAlchemy async engine and an async sessionmaker.
    """
    # Configure connection pool for better concurrent operation handling
    connect_args = {}
    if "asyncpg" in database_url:
        connect_args = {
            "server_settings": {
                "application_name": "ecommerce_app",
                "statement_timeout": "30000",  # 30 seconds
                "idle_in_transaction_session_timeout": "30000",  # 30 seconds
            }
        }

    engine = create_async_engine(
        database_url,
        echo=echo,
        pool_size=20,  # Maintain 20 connections in pool
        max_overflow=30,  # Allow up to 30 additional connections
        pool_pre_ping=True,  # Test connections before use
        pool_recycle=300,  # Recycle connections every 5 minutes
        connect_args=connect_args,
    )
    AsyncSessionLocal = async_sessionmaker(
        expire_on_commit=False,
        autoflush=False,
        autocommit=False,
        bind=engine,
        class_=AsyncSession,
    )
    return engine, AsyncSessionLocal


def get_sync_db_components(database_url: str, echo: bool = False) -> Tuple[Any, Any]:
    """
    Returns a SQLAlchemy sync engine and sessionmaker for migrations and sync operations.
    """
    # Convert async URL to sync URL for migrations
    sync_url = database_url.replace("+asyncpg", "").replace("+aiosqlite", "")
    
    settings = get_settings()
    engine = create_engine(sync_url, echo=echo)
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return engine, SessionLocal


settings = get_settings()

# Global async engine and sessionmaker to avoid creating multiple engines
_async_engine, AsyncSessionLocal = get_db_components(settings.DATABASE_URL, echo=False)

# Function to get the async session factory
def get_async_session_factory():
    """Return the async session factory for creating database sessions, bound to the current event loop."""
    return AsyncSessionLocal

# Dependency to get an async database session
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    AsyncSessionLocal = get_async_session_factory() # Get factory dynamically
    db = AsyncSessionLocal()
    try:
        yield db
    finally:
        await db.close()


# Create sync engine and session local for migrations
sync_engine, SyncSessionLocal = get_sync_db_components(settings.DATABASE_URL, echo=False)

# Alias for backward compatibility
SessionLocal = SyncSessionLocal

# Dependency to get a sync database session (for migrations, etc.)
def get_sync_db():
    db = SyncSessionLocal()
    try:
        yield db
    finally:
        db.close()


# Function to get the session factory for services that need it
def get_db_session_factory():
    """Return the session factory for creating database sessions."""
    return get_async_session_factory()


# Function to get the sync session factory for creating database sessions.
def get_sync_db_session_factory():
    """Return the sync session factory for creating database sessions."""
    return SyncSessionLocal

import core.db.models # Import models to ensure they are registered with Base.metadata