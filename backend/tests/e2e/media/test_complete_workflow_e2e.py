"""
Complete End-to-End Workflow Test for Media Generation.

This test demonstrates the entire media generation workflow from start to finish,
including generation, status tracking, and publishing.
"""

import asyncio
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from unittest.mock import patch

from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.stores.models import Store


class TestCompleteWorkflowE2E:
    """Complete end-to-end workflow tests."""

    @pytest.mark.asyncio
    async def test_complete_media_generation_and_push_workflow(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """
        Test the complete workflow:
        1. Generate media for multiple products
        2. Track job progress
        3. Wait for completion
        4. Push to platform
        5. Verify final state
        """
        
        # Step 1: Submit media generation request for multiple products
        generation_request = {
            "mode": "image",
            "media_type": "image",
            "model": "banana",
            "shop_id": test_store.id,
            "items": [
                {
                    "product_id": 1001,
                    "product_context": {
                        "title": "Premium Wireless Headphones",
                        "description": "High-quality wireless headphones with active noise cancellation",
                        "category": "electronics",
                        "price": 299.99,
                        "currency": "USD",
                        "colors": ["black", "white", "silver"],
                        "key_features": ["wireless", "noise cancellation", "long battery life", "premium sound"]
                    }
                },
                {
                    "product_id": 1002,
                    "product_context": {
                        "title": "Running Shoes",
                        "description": "Lightweight running shoes for professional athletes",
                        "category": "footwear",
                        "price": 159.99,
                        "currency": "USD",
                        "colors": ["red", "blue", "black"],
                        "key_features": ["lightweight", "breathable", "durable", "performance"]
                    }
                }
            ],
            "settings": {
                "aspect_ratio": "1:1",
                "guidance": 7.5,
                "steps": 25,
                "upscale": True,
                "quality": "high"
            },
            "target_platforms": ["shopify", "instagram"],
            "campaign_theme": "Summer Collection 2024"
        }
        
        print("🚀 Step 1: Submitting media generation request...")
        response = await e2e_client.post("/api/media/generate", json=generation_request)
        
        assert response.status_code == 200
        generation_data = response.json()
        assert "jobs" in generation_data
        assert len(generation_data["jobs"]) == 2  # Two products
        
        job_ids = []
        db_job_ids = []
        
        for job_info in generation_data["jobs"]:
            job_id = job_info["job_id"]
            job_ids.append(job_id)
            cleanup_test_jobs(job_id)
            
            # Get database job ID
            db_job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.job_id == job_id)
            )
            db_job = db_job_result.scalar_one_or_none()
            assert db_job is not None
            db_job_ids.append(db_job.id)
        
        print(f"✅ Jobs created: {job_ids}")
        
        # Step 2: Track job progress
        print("📊 Step 2: Tracking job progress...")
        
        completed_jobs = []
        for db_job_id in db_job_ids:
            print(f"  Waiting for job {db_job_id} to complete...")
            completed_job = await wait_for_job_completion(e2e_client, db_job_id)
            assert completed_job["status"] == "completed"
            assert len(completed_job["variants"]) == 4  # 4 image variants per job
            completed_jobs.append(completed_job)
        
        print("✅ All jobs completed successfully")
        
        # Step 3: Verify database state
        print("🔍 Step 3: Verifying database state...")
        
        for db_job_id in db_job_ids:
            # Check job status
            job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.id == db_job_id)
            )
            job = job_result.scalar_one_or_none()
            assert job.status == MediaJobStatus.COMPLETED
            assert job.completed_at is not None
            
            # Check variants
            variants_result = await db_session.execute(
                select(MediaVariant).filter(MediaVariant.job_id == db_job_id)
            )
            variants = variants_result.scalars().all()
            assert len(variants) == 4
            
            for variant in variants:
                assert variant.status == MediaVariantStatus.READY
                assert variant.image_url is not None
                assert "test-storage.com" in variant.image_url  # From mock
        
        print("✅ Database state verified")
        
        # Step 4: List jobs to verify they appear in user's job list
        print("📋 Step 4: Verifying job listing...")
        
        jobs_response = await e2e_client.get("/api/media/jobs?per_page=10")
        assert jobs_response.status_code == 200
        
        jobs_data = jobs_response.json()
        assert jobs_data["total"] >= 2
        
        # Find our jobs in the list
        our_job_ids = set(str(db_id) for db_id in db_job_ids)
        listed_job_ids = set(str(job["job_id"]) for job in jobs_data["jobs"])
        
        assert our_job_ids.issubset(listed_job_ids), "Our jobs should appear in the job list"
        
        print("✅ Jobs appear in listing")
        
        # Step 5: Push media to platform
        print("🚀 Step 5: Pushing media to Shopify...")
        
        # Mock Shopify service for push
        with patch("modules.stores.shopify_service.ShopifyService.upload_product_media") as mock_shopify:
            mock_shopify.return_value = {
                "success": True,
                "media_id": "shopify_media_12345",
                "url": "https://cdn.shopify.com/media/12345.png"
            }
            
            # Push first job's first variant
            first_job = completed_jobs[0]
            first_variant = first_job["variants"][0]
            
            push_request = {
                "shop_id": test_store.id,
                "product_id": 1001,
                "variant_id": first_variant["variant_id"],
                "publish_targets": ["shopify"],
                "publish_options": {
                    "alt_text": "Premium wireless headphones in multiple colors",
                    "position": 1,
                    "replace_existing": False
                }
            }
            
            push_response = await e2e_client.post("/api/media/push", json=push_request)
            assert push_response.status_code == 200
            
            push_data = push_response.json()
            assert push_data["success"] is True
            assert push_data["status"] == "completed"
            
            # Verify Shopify service was called
            mock_shopify.assert_called_once()
            
        print("✅ Media pushed to Shopify successfully")
        
        # Step 6: Final verification - check job status one more time
        print("🔍 Step 6: Final verification...")
        
        for db_job_id in db_job_ids:
            final_status_response = await e2e_client.get(f"/api/media/jobs/{db_job_id}/status")
            assert final_status_response.status_code == 200
            
            final_status = final_status_response.json()
            assert final_status["status"] == "completed"
            assert final_status["progress"] == 100.0
            assert len(final_status["variants"]) == 4
            
            # Verify all variants are ready
            for variant in final_status["variants"]:
                assert variant["status"] == "ready"
                assert variant["image_url"] is not None
        
        print("✅ Complete workflow test passed!")
        
        # Summary
        print("\n🎉 WORKFLOW SUMMARY:")
        print(f"   • Generated media for {len(generation_data['jobs'])} products")
        print(f"   • Created {sum(len(job['variants']) for job in completed_jobs)} media variants")
        print(f"   • Successfully pushed media to Shopify")
        print(f"   • All jobs completed in 'completed' status")
        print(f"   • All variants in 'ready' status")

    @pytest.mark.asyncio
    async def test_workflow_with_mixed_media_types(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test workflow with different media types for the same product."""
        
        product_id = 2001
        
        # Generate different media types for the same product
        media_requests = [
            {
                "mode": "image",
                "media_type": "image",
                "model": "banana",
                "shop_id": test_store.id,
                "items": [{
                    "product_id": product_id,
                    "product_context": {
                        "title": "Luxury Watch",
                        "description": "Premium Swiss-made luxury watch",
                        "category": "accessories",
                        "price": 2999.99
                    }
                }]
            },
            {
                "mode": "video",
                "media_type": "video", 
                "model": "veo3",
                "shop_id": test_store.id,
                "items": [{
                    "product_id": product_id,
                    "product_context": {
                        "title": "Luxury Watch",
                        "description": "Premium Swiss-made luxury watch",
                        "category": "accessories",
                        "price": 2999.99
                    }
                }]
            },
            {
                "mode": "text",
                "media_type": "text",
                "model": "gemini",
                "shop_id": test_store.id,
                "items": [{
                    "product_id": product_id,
                    "product_context": {
                        "title": "Luxury Watch",
                        "description": "Premium Swiss-made luxury watch",
                        "category": "accessories",
                        "price": 2999.99
                    }
                }]
            }
        ]
        
        print("🚀 Generating multiple media types for same product...")
        
        all_jobs = []
        
        # Submit all requests
        for i, request in enumerate(media_requests):
            print(f"  Submitting {request['mode']} generation request...")
            response = await e2e_client.post("/api/media/generate", json=request)
            assert response.status_code == 200
            
            job_info = response.json()["jobs"][0]
            job_id = job_info["job_id"]
            cleanup_test_jobs(job_id)
            
            # Get database job ID
            db_job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.job_id == job_id)
            )
            db_job = db_job_result.scalar_one_or_none()
            all_jobs.append((request['mode'], db_job.id))
        
        # Wait for all jobs to complete
        print("⏳ Waiting for all jobs to complete...")
        
        for media_type, db_job_id in all_jobs:
            print(f"  Waiting for {media_type} job {db_job_id}...")
            completed_job = await wait_for_job_completion(e2e_client, db_job_id)
            assert completed_job["status"] == "completed"
            
            # Verify media type specific results
            if media_type == "image":
                assert len(completed_job["variants"]) == 4
                for variant in completed_job["variants"]:
                    assert variant["image_url"] is not None
            elif media_type == "video":
                assert len(completed_job["variants"]) == 4
                for variant in completed_job["variants"]:
                    assert variant["video_url"] is not None
                    assert variant["duration"] is not None
        
        print("✅ All media types generated successfully for the same product!")

    @pytest.mark.asyncio
    async def test_workflow_error_recovery(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        cleanup_test_jobs
    ):
        """Test workflow behavior when some jobs fail."""
        
        # Mock one provider to fail, others to succeed
        with patch("modules.media.providers.image.banana.BananaImageProvider.generate_media") as mock_banana:
            # First call fails, second succeeds
            mock_banana.side_effect = [
                Exception("Provider temporarily unavailable"),
                # This would be the retry or second product
            ]
            
            request_data = {
                "mode": "image",
                "media_type": "image",
                "model": "banana",
                "shop_id": test_store.id,
                "items": [
                    {
                        "product_id": 3001,
                        "product_context": {
                            "title": "Product That Will Fail",
                            "description": "This product generation will fail"
                        }
                    }
                ]
            }
            
            print("🚀 Testing error recovery workflow...")
            
            response = await e2e_client.post("/api/media/generate", json=request_data)
            
            # Should still create job successfully
            assert response.status_code == 200
            
            job_info = response.json()["jobs"][0]
            cleanup_test_jobs(job_info["job_id"])
            
            print("✅ Error recovery workflow handled gracefully")

    @pytest.mark.asyncio
    async def test_concurrent_workflow_processing(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        cleanup_test_jobs
    ):
        """Test multiple concurrent workflows."""
        
        print("🚀 Testing concurrent workflow processing...")
        
        # Submit multiple requests concurrently
        concurrent_requests = []
        for i in range(3):
            request = {
                "mode": "image",
                "media_type": "image",
                "model": "banana",
                "shop_id": test_store.id,
                "items": [{
                    "product_id": 4000 + i,
                    "product_context": {
                        "title": f"Concurrent Product {i}",
                        "description": f"Product for concurrent test {i}"
                    }
                }]
            }
            concurrent_requests.append(request)
        
        # Submit all requests
        tasks = []
        for request in concurrent_requests:
            task = e2e_client.post("/api/media/generate", json=request)
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        
        # Verify all requests succeeded
        job_ids = []
        for response in responses:
            assert response.status_code == 200
            job_info = response.json()["jobs"][0]
            job_ids.append(job_info["job_id"])
            cleanup_test_jobs(job_info["job_id"])
        
        print(f"✅ {len(job_ids)} concurrent workflows submitted successfully")
        
        # Verify jobs were created in database
        for job_id in job_ids:
            db_job_result = await db_session.execute(
                select(MediaJob).filter(MediaJob.job_id == job_id)
            )
            db_job = db_job_result.scalar_one_or_none()
            assert db_job is not None
            assert db_job.status in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING, MediaJobStatus.COMPLETED]
        
        print("✅ Concurrent workflow processing test completed")
