"""add_credits_to_tenants

Revision ID: 2440f2a2652d
Revises: 70284ab48241
Create Date: 2025-09-16 00:07:29.539972

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2440f2a2652d'
down_revision: Union[str, Sequence[str], None] = '70284ab48241'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenants', sa.Column('credits', sa.DECIMAL(precision=10, scale=2), nullable=True, default=0.00))
    op.add_column('tenants', sa.Column('credit_expires_at', sa.DateTime(timezone=True), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('tenants', 'credit_expires_at')
    op.drop_column('tenants', 'credits')
    # ### end Alembic commands ###
