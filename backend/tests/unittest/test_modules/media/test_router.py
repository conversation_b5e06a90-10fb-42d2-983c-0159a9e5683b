"""
Comprehensive tests for media router endpoints.
Tests all API endpoints, authentication, validation, and error handling.
"""

import pytest
from unittest.mock import AsyncMock, patch
from fastapi.testclient import TestClient
from fastapi import status

from modules.media.schemas import (
    MediaGenerateRequest, ProductItem, ProductContext, ProductCategory,
    GenerationSettings, MediaPushRequest, PublishOptions
)
from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.auth.models import User


class TestMediaGenerateEndpoint:
    """Test /generate endpoint."""
    
    @pytest.mark.asyncio
    async def test_generate_media_success(self, client, auth_headers: dict, sample_media_request_dict, test_tenant, test_store):
        """Test successful media generation request."""
        with patch('modules.media.service.media_service.create_generation_jobs') as mock_create:
            with patch('modules.queue.queue_service.celery_service.enqueue_media_generation') as mock_enqueue:
                # Mock job creation
                mock_job = MediaJob(
                    id=1,
                    user_id=1,
                    product_id=123,
                    status=MediaJobStatus.PENDING,
                    media_type="image",
                    provider="banana"
                )
                mock_create.return_value = [mock_job]
                mock_enqueue.return_value = "test-celery-task-id"

                response = await client.post(
                    "/api/media/generate",
                    json=sample_media_request_dict,
                    headers=auth_headers
                )

                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "jobs" in data
                assert len(data["jobs"]) == 1
                assert data["jobs"][0]["status"] == "pending"
    
    @pytest.mark.asyncio
    async def test_generate_media_unauthorized(self, client, sample_media_request_dict):
        """Test media generation without authentication."""
        response = await client.post(
            "/api/media/generate",
            json=sample_media_request_dict
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN
    
    @pytest.mark.asyncio
    async def test_generate_media_invalid_request(self, client, auth_headers: dict, test_tenant, test_store):
        """Test media generation with invalid request data."""
        invalid_request = {
            "mode": "image",
            "items": [],  # Empty items list
            "product_ids": []
        }

        with patch('modules.media.service.media_service.create_generation_jobs') as mock_create:
            with patch('modules.queue.queue_service.celery_service.enqueue_media_generation') as mock_enqueue:
                mock_create.return_value = []
                mock_enqueue.return_value = "test-celery-task-id"

                response = await client.post(
                    "/api/media/generate",
                    json=invalid_request,
                    headers=auth_headers
                )

                # Empty product_ids is valid and returns empty jobs array
                assert response.status_code == status.HTTP_200_OK
                data = response.json()
                assert "jobs" in data
                assert data["jobs"] == []
    
    @pytest.mark.asyncio
    async def test_generate_media_service_error(self, client, auth_headers: dict, sample_media_request_dict):
        """Test media generation with service error."""
        with patch('modules.media.service.media_service.create_generation_jobs') as mock_create:
            mock_create.side_effect = Exception("Service unavailable")

            response = await client.post(
                "/api/media/generate",
                json=sample_media_request_dict,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            data = response.json()
            assert "detail" in data


class TestJobStatusEndpoint:
    """Test /jobs/{job_id}/status endpoint."""
    
    @pytest.mark.asyncio
    async def test_get_job_status_success(self, client, auth_headers: dict, test_user: User):
        """Test successful job status retrieval."""
        with patch('modules.media.service.media_service.get_job_status') as mock_status:
            mock_status.return_value = {
                "job_id": 1,
                "status": "processing",
                "progress": 75.0,
                "variants": [
                    {
                        "variant_id": 1,
                        "variant_name": "square",
                        "status": "ready",
                        "image_url": "https://example.com/image.jpg"
                    }
                ]
            }

            response = await client.get(
                "/api/media/jobs/1/status",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["job_id"] == 1
            assert data["status"] == "processing"
            assert data["progress"] == 75.0
            assert len(data["variants"]) == 1
    
    @pytest.mark.asyncio
    async def test_get_job_status_not_found(self, client, auth_headers: dict):
        """Test job status for non-existent job."""
        with patch('modules.media.service.media_service.get_job_status') as mock_status:
            mock_status.return_value = None

            response = await client.get(
                "/api/media/jobs/999/status",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_get_job_status_unauthorized(self, client):
        """Test job status without authentication."""
        response = await client.get("/api/media/jobs/1/status")

        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestJobListEndpoint:
    """Test /jobs endpoint."""
    
    @pytest.mark.asyncio
    async def test_list_jobs_success(self, client, auth_headers: dict, test_user: User):
        """Test successful job listing."""
        with patch('modules.media.service.media_service.list_user_jobs') as mock_list:
            mock_list.return_value = {
                "jobs": [
                    {
                        "job_id": "1",
                        "product_id": 123,
                        "status": "completed",
                        "media_type": "image",
                        "created_at": "2024-01-01T00:00:00Z"
                    },
                    {
                        "job_id": "2",
                        "product_id": 124,
                        "status": "processing",
                        "media_type": "video",
                        "created_at": "2024-01-02T00:00:00Z"
                    }
                ],
                "total": 2,
                "page": 1,
                "per_page": 10
            }

            response = await client.get(
                "/api/media/jobs",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["jobs"]) == 2
            assert data["total"] == 2
            assert data["page"] == 1
    
    @pytest.mark.asyncio
    async def test_list_jobs_with_filters(self, client, auth_headers: dict):
        """Test job listing with filters."""
        with patch('modules.media.service.media_service.list_user_jobs') as mock_list:
            mock_list.return_value = {
                "jobs": [
                    {
                        "job_id": "1",
                        "product_id": 123,
                        "status": "completed",
                        "media_type": "image",
                        "created_at": "2024-01-01T00:00:00Z"
                    }
                ],
                "total": 1,
                "page": 1,
                "per_page": 5
            }

            response = await client.get(
                "/api/media/jobs?status=completed&page=1&per_page=5",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert len(data["jobs"]) == 1
            assert data["jobs"][0]["status"] == "completed"
    
    @pytest.mark.asyncio
    async def test_list_jobs_unauthorized(self, client):
        """Test job listing without authentication."""
        response = await client.get("/api/media/jobs")

        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestJobCancelEndpoint:
    """Test /jobs/{job_id}/cancel endpoint."""
    
    @pytest.mark.asyncio
    async def test_cancel_job_success(self, client, auth_headers: dict):
        """Test successful job cancellation."""
        with patch('modules.media.service.media_service.cancel_job') as mock_cancel:
            mock_cancel.return_value = True

            response = await client.post(
                "/api/media/jobs/1/cancel",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["message"] == "Job cancelled successfully"
    
    @pytest.mark.asyncio
    async def test_cancel_job_not_found(self, client, auth_headers: dict):
        """Test cancelling non-existent job."""
        with patch('modules.media.service.media_service.cancel_job') as mock_cancel:
            mock_cancel.return_value = False

            response = await client.post(
                "/api/media/jobs/999/cancel",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_404_NOT_FOUND
    
    @pytest.mark.asyncio
    async def test_cancel_job_unauthorized(self, client):
        """Test job cancellation without authentication."""
        response = await client.post("/api/media/jobs/1/cancel")

        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestJobRetryEndpoint:
    """Test /jobs/{job_id}/retry endpoint."""
    
    @pytest.mark.asyncio
    async def test_retry_job_success(self, client, auth_headers: dict):
        """Test successful job retry."""
        with patch('modules.media.service.media_service.retry_job') as mock_retry:
            mock_job = MediaJob(
                id=1,
                user_id=1,
                product_id=123,
                status=MediaJobStatus.PENDING,
                media_type="image",
                provider="banana"
            )
            mock_retry.return_value = mock_job

            response = await client.post(
                "/api/media/jobs/1/retry",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["message"] == "Job retry initiated successfully"
            assert data["new_job_id"] == 1
    
    @pytest.mark.asyncio
    async def test_retry_job_not_found(self, client, auth_headers: dict):
        """Test retrying non-existent job."""
        with patch('modules.media.service.media_service.retry_job') as mock_retry:
            mock_retry.return_value = None

            response = await client.post(
                "/api/media/jobs/999/retry",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_404_NOT_FOUND


class TestMediaPushEndpoint:
    """Test /push endpoint."""
    
    @pytest.mark.asyncio
    async def test_push_media_success(self, client, auth_headers: dict, test_store):
        """Test successful media push."""
        push_request = {
            "shop_id": test_store.id,  # Use the actual test store ID
            "product_id": 123,
            "variant_id": 1,
            "publish_targets": ["shopify"],
            "publish_options": {
                "alt_text": "Premium wireless headphones",
                "position": 1,
                "filename": "headphones_square.jpg"
            }
        }

        with patch('modules.media.service.media_service.push_media_to_platforms') as mock_push:
            mock_push.return_value = {
                "success": True,
                "results": {
                    "shopify": {
                        "success": True,
                        "platform_id": "shopify_img_123",
                        "url": "https://shopify.example.com/image.jpg"
                    }
                }
            }

            response = await client.post(
                "/api/media/push",
                json=push_request,
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["success"] is True
            assert "shopify" in data["results"]
            assert data["results"]["shopify"]["success"] is True
    
    @pytest.mark.asyncio
    async def test_push_media_invalid_request(self, client, auth_headers: dict):
        """Test media push with invalid request."""
        invalid_request = {
            "shop_id": 1,
            # Missing required fields
        }

        response = await client.post(
            "/api/media/push",
            json=invalid_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    
    @pytest.mark.asyncio
    async def test_push_media_unauthorized(self, client):
        """Test media push without authentication."""
        push_request = {
            "shop_id": 1,
            "product_id": 123,
            "variant_id": 1,
            "publish_targets": ["shopify"]
        }

        response = await client.post(
            "/api/media/push",
            json=push_request
        )

        assert response.status_code == status.HTTP_403_FORBIDDEN


class TestErrorHandling:
    """Test error handling across endpoints."""
    
    @pytest.mark.asyncio
    async def test_internal_server_error_handling(self, client, auth_headers: dict):
        """Test internal server error handling."""
        with patch('modules.media.service.media_service.list_user_jobs') as mock_list:
            mock_list.side_effect = Exception("Database connection failed")

            response = await client.get(
                "/api/media/jobs",
                headers=auth_headers
            )

            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
            data = response.json()
            assert "detail" in data  # FastAPI uses "detail" for error messages
    
    @pytest.mark.asyncio
    async def test_validation_error_handling(self, client, auth_headers: dict):
        """Test validation error handling."""
        invalid_request = {
            "mode": "invalid_mode",  # Invalid enum value
            "items": [{"invalid": "data"}],
            "product_ids": ["not_an_integer"]
        }

        response = await client.post(
            "/api/media/generate",
            json=invalid_request,
            headers=auth_headers
        )

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "detail" in data
