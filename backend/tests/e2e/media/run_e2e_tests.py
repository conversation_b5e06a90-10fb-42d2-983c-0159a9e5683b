#!/usr/bin/env python3
"""
E2E Test Runner for Media Generation APIs

This script runs the complete E2E test suite for media generation,
including setup and teardown of required services.
"""

import os
import sys
import subprocess
import time
import signal
import argparse
from pathlib import Path
from typing import Optional, List


class E2ETestRunner:
    """Manages E2E test execution with service orchestration."""
    
    def __init__(self, backend_dir: Path):
        self.backend_dir = backend_dir
        self.src_dir = backend_dir / "src"
        self.test_dir = backend_dir / "tests" / "e2e" / "media"
        self.processes: List[subprocess.Popen] = []
        
    def setup_environment(self):
        """Set up environment variables for testing."""
        os.environ["TESTING"] = "True"
        os.environ["PYTHONPATH"] = str(self.src_dir)
        
        # Ensure Redis is available for queue
        redis_url = os.environ.get("REDIS_URL", "redis://localhost:6379/0")
        os.environ["REDIS_URL"] = redis_url
        
        print(f"✓ Environment configured")
        print(f"  - Backend dir: {self.backend_dir}")
        print(f"  - Source dir: {self.src_dir}")
        print(f"  - Test dir: {self.test_dir}")
        print(f"  - Redis URL: {redis_url}")
    
    def check_dependencies(self):
        """Check that required services are available."""
        print("Checking dependencies...")
        
        # Check Redis
        try:
            import redis
            r = redis.Redis.from_url(os.environ["REDIS_URL"])
            r.ping()
            print("✓ Redis is available")
        except Exception as e:
            print(f"✗ Redis not available: {e}")
            print("  Please start Redis server: redis-server")
            return False
        
        # Check PostgreSQL
        try:
            from core.config import get_settings
            settings = get_settings()
            print(f"✓ Database URL configured: {settings.DATABASE_URL}")
        except Exception as e:
            print(f"✗ Database configuration error: {e}")
            return False
        
        return True
    
    def start_worker(self) -> Optional[subprocess.Popen]:
        """Start Celery worker for E2E testing."""
        print("Starting Celery worker...")
        
        worker_cmd = [
            "uv", "run", "python", "-m", "celery",
            "-A", "servers.worker.main", "worker",
            "--loglevel=info", "--concurrency=2",
            "-Q", "media-generation,media-push,celery"
        ]
        
        try:
            worker_process = subprocess.Popen(
                worker_cmd,
                cwd=str(self.src_dir),
                env=os.environ.copy(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            # Wait for worker to start
            time.sleep(5)
            
            if worker_process.poll() is not None:
                stdout, stderr = worker_process.communicate()
                print(f"✗ Worker failed to start")
                print(f"STDOUT: {stdout.decode()}")
                print(f"STDERR: {stderr.decode()}")
                return None
            
            print("✓ Celery worker started")
            self.processes.append(worker_process)
            return worker_process
            
        except Exception as e:
            print(f"✗ Failed to start worker: {e}")
            return None
    
    def run_tests(self, test_pattern: str = "", verbose: bool = False) -> bool:
        """Run the E2E tests."""
        print(f"Running E2E tests...")
        
        # Build pytest command
        pytest_cmd = ["uv", "run", "python", "-m", "pytest"]
        
        if verbose:
            pytest_cmd.append("-v")
        
        # Add test directory
        if test_pattern:
            pytest_cmd.append(f"{self.test_dir}/{test_pattern}")
        else:
            pytest_cmd.append(str(self.test_dir))
        
        # Add pytest options
        pytest_cmd.extend([
            "--tb=short",
            "--disable-warnings",
            "-x"  # Stop on first failure
        ])
        
        print(f"Command: {' '.join(pytest_cmd)}")
        
        try:
            result = subprocess.run(
                pytest_cmd,
                cwd=str(self.backend_dir),
                env=os.environ.copy(),
                timeout=600  # 10 minute timeout
            )
            
            if result.returncode == 0:
                print("✓ All tests passed!")
                return True
            else:
                print(f"✗ Tests failed with exit code {result.returncode}")
                return False
                
        except subprocess.TimeoutExpired:
            print("✗ Tests timed out after 10 minutes")
            return False
        except Exception as e:
            print(f"✗ Error running tests: {e}")
            return False
    
    def cleanup(self):
        """Clean up started processes."""
        print("Cleaning up...")
        
        for process in self.processes:
            try:
                # Send SIGTERM to process group
                os.killpg(os.getpgid(process.pid), signal.SIGTERM)
                process.wait(timeout=10)
                print("✓ Process terminated gracefully")
            except (subprocess.TimeoutExpired, ProcessLookupError):
                try:
                    # Force kill if graceful shutdown fails
                    os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                    print("✓ Process force killed")
                except ProcessLookupError:
                    pass
            except Exception as e:
                print(f"Warning: Error cleaning up process: {e}")
        
        self.processes.clear()
    
    def run_full_suite(self, test_pattern: str = "", verbose: bool = False) -> bool:
        """Run the complete E2E test suite."""
        try:
            # Setup
            self.setup_environment()
            
            if not self.check_dependencies():
                return False
            
            # Start services
            worker = self.start_worker()
            if not worker:
                return False
            
            # Run tests
            success = self.run_tests(test_pattern, verbose)
            
            return success
            
        except KeyboardInterrupt:
            print("\n✗ Tests interrupted by user")
            return False
        except Exception as e:
            print(f"✗ Unexpected error: {e}")
            return False
        finally:
            self.cleanup()


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Run E2E tests for Media Generation APIs")
    parser.add_argument(
        "--pattern", "-p",
        default="",
        help="Test file pattern to run (e.g., 'test_media_generation_e2e.py')"
    )
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Run tests in verbose mode"
    )
    parser.add_argument(
        "--backend-dir",
        type=Path,
        default=Path(__file__).parent.parent.parent.parent,
        help="Path to backend directory"
    )
    
    args = parser.parse_args()
    
    # Validate backend directory
    if not args.backend_dir.exists():
        print(f"✗ Backend directory not found: {args.backend_dir}")
        sys.exit(1)
    
    # Create and run test runner
    runner = E2ETestRunner(args.backend_dir)
    success = runner.run_full_suite(args.pattern, args.verbose)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
