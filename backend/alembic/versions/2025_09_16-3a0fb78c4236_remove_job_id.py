"""remove job_id

Revision ID: 3a0fb78c4236
Revises: 7115c89c38cd
Create Date: 2025-09-16 04:46:07.933304

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '3a0fb78c4236'
down_revision: Union[str, Sequence[str], None] = '7115c89c38cd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('media_jobs', 'job_id')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('media_jobs', sa.Column('job_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    # ### end Alembic commands ###
