# Media Generation E2E Tests

This directory contains comprehensive End-to-End (E2E) tests for the Media Generation APIs. These tests verify the complete workflow from API request to job completion, including real database operations, queue processing, and worker execution.

## Overview

The E2E tests cover the entire media generation pipeline:

1. **API Request Handling** - FastAPI endpoint processing
2. **Database Operations** - Job and variant creation/updates
3. **Queue Integration** - Celery task enqueuing
4. **Worker Processing** - Background job execution
5. **Provider Integration** - Media generation (mocked)
6. **Response Handling** - Status tracking and results

## Test Structure

```
tests/e2e/media/
├── __init__.py
├── conftest.py                     # E2E test configuration and fixtures
├── test_media_generation_e2e.py    # Core generation workflow tests
├── test_media_push_e2e.py          # Media publishing tests
├── test_job_management_e2e.py      # Job listing and management tests
├── test_error_handling_e2e.py      # Error scenarios and edge cases
├── run_e2e_tests.py               # Test runner script
└── README.md                      # This file
```

## What's Tested End-to-End

### ✅ Real Components (Not Mocked)
- FastAPI application and routing
- Database operations (PostgreSQL)
- SQLAlchemy ORM operations
- Celery queue service (Redis)
- Worker process execution
- Job status tracking
- Authentication and authorization
- Request validation
- Response formatting

### 🎭 Mocked Components
- **Media Providers Only**:
  - `modules.media.providers.text.gemini.GeminiTextProvider`
  - `modules.media.providers.image.banana.BananaImageProvider`
  - `modules.media.providers.video.veo3.Veo3VideoProvider`

This ensures we test the complete system integration while avoiding external API calls and costs.

## Test Categories

### 1. Media Generation Tests (`test_media_generation_e2e.py`)
- Image generation workflow
- Video generation workflow  
- Text generation workflow
- Job status tracking
- Multiple product processing
- Job cancellation

### 2. Media Push Tests (`test_media_push_e2e.py`)
- Push to Shopify platform
- Multiple platform publishing
- Custom publishing options
- Authorization validation
- Platform failure handling

### 3. Job Management Tests (`test_job_management_e2e.py`)
- Job listing with pagination
- Status filtering
- Product filtering
- Detailed status checking
- Job retry functionality
- Progress tracking
- Concurrent processing

### 4. Error Handling Tests (`test_error_handling_e2e.py`)
- Invalid request formats
- Unauthorized access
- Provider failures
- Database issues
- Queue service failures
- Network timeouts
- Storage failures

## Prerequisites

### Required Services
1. **PostgreSQL** - Database for job storage
2. **Redis** - Queue backend for Celery
3. **Python 3.12+** - Runtime environment

### Environment Setup
```bash
# Install dependencies
uv pip install -e ".[all]"

# Start Redis (if not running)
redis-server

# Ensure PostgreSQL is running and configured
# Database URL should be set in environment
```

## Running the Tests

### Quick Start
```bash
# Run all E2E tests
python tests/e2e/media/run_e2e_tests.py

# Run specific test file
python tests/e2e/media/run_e2e_tests.py --pattern "test_media_generation_e2e.py"

# Run with verbose output
python tests/e2e/media/run_e2e_tests.py --verbose
```

### Manual Execution
```bash
# From backend directory
cd backend

# Run with pytest directly
uv run python -m pytest tests/e2e/media/ -v

# Run specific test
uv run python -m pytest tests/e2e/media/test_media_generation_e2e.py::TestMediaGenerationE2E::test_generate_image_e2e_workflow -v
```

### Environment Variables
```bash
export TESTING=True
export REDIS_URL=redis://localhost:6379/0
export DATABASE_URL=postgresql+asyncpg://user:pass@localhost/test_db
```

## Test Fixtures

### Key Fixtures (from `conftest.py`)
- `worker_process` - Starts Celery worker for test session
- `mock_providers` - Mocks all media providers with test data
- `e2e_client` - Authenticated HTTP client with worker running
- `sample_media_requests` - Pre-configured test requests
- `wait_for_job_completion` - Helper to wait for async job completion
- `cleanup_test_jobs` - Automatic test job cleanup

### Inherited Fixtures (from main `conftest.py`)
- `db_session` - Database session
- `test_user` - Authenticated test user
- `test_store` - Test store for the user
- `authenticated_client` - HTTP client with auth headers

## Test Data

### Mock Provider Responses
The tests use realistic mock data that simulates actual provider responses:

```python
# Image Provider (Banana)
{
    "success": True,
    "provider_job_id": "test_banana_job_123",
    "images": [
        {
            "image_url": "https://test-storage.com/images/banana_image_0.png",
            "metadata": {"width": 1024, "height": 1024, "format": "PNG"}
        }
        # ... 4 variants total
    ]
}

# Video Provider (Veo3)
{
    "success": True,
    "provider_job_id": "test_veo3_job_456", 
    "variants": [
        {
            "video_url": "https://test-storage.com/videos/veo3_video_0.mp4",
            "thumbnail_url": "https://test-storage.com/thumbnails/veo3_thumb_0.jpg",
            "duration_seconds": 15.0
        }
        # ... 4 variants total
    ]
}
```

## Debugging Tests

### Common Issues

1. **Worker Not Starting**
   ```bash
   # Check Redis connection
   redis-cli ping
   
   # Check Celery configuration
   cd backend/src
   uv run python -m celery inspect active -A servers.worker.celery_app:celery_app
   ```

2. **Database Connection Issues**
   ```bash
   # Verify database URL
   echo $DATABASE_URL
   
   # Test connection
   psql $DATABASE_URL -c "SELECT 1;"
   ```

3. **Test Timeouts**
   - Increase timeout in `wait_for_job_completion`
   - Check worker logs for processing issues
   - Verify mock providers are working

### Logging
Tests use structured logging. To see detailed logs:
```bash
export LOG_LEVEL=DEBUG
python tests/e2e/media/run_e2e_tests.py --verbose
```

## Performance Considerations

### Test Execution Time
- Full suite: ~5-10 minutes
- Individual test: ~30-60 seconds
- Worker startup: ~5 seconds

### Resource Usage
- Memory: ~500MB (including worker)
- CPU: Moderate (2 worker processes)
- Network: Minimal (only mocked external calls)

## Extending the Tests

### Adding New Test Cases
1. Create test method in appropriate test class
2. Use existing fixtures for setup
3. Follow naming convention: `test_<scenario>_e2e`
4. Include cleanup with `cleanup_test_jobs`

### Adding New Mock Providers
1. Update `mock_providers` fixture in `conftest.py`
2. Add realistic response data
3. Ensure error scenarios are covered

### Testing New APIs
1. Create new test file: `test_<feature>_e2e.py`
2. Import base fixtures from `conftest.py`
3. Add feature-specific fixtures as needed

## CI/CD Integration

### GitHub Actions Example
```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
    
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.12'
      - name: Install dependencies
        run: |
          pip install uv
          uv pip install -e ".[all]"
      - name: Run E2E tests
        env:
          DATABASE_URL: postgresql+asyncpg://postgres:postgres@localhost/postgres
          REDIS_URL: redis://localhost:6379/0
        run: |
          python tests/e2e/media/run_e2e_tests.py
```

## Troubleshooting

### Common Error Messages

1. **"Worker failed to start"**
   - Check Redis connection
   - Verify PYTHONPATH includes src/
   - Check for port conflicts

2. **"Job did not complete within timeout"**
   - Increase timeout value
   - Check worker is processing jobs
   - Verify mock providers are responding

3. **"Access denied: shop does not belong to user"**
   - Ensure test_store fixture is used
   - Check authentication headers
   - Verify user-store relationship

### Getting Help
- Check test logs for detailed error messages
- Verify all prerequisites are met
- Ensure environment variables are set correctly
- Review the test fixtures and mock data
