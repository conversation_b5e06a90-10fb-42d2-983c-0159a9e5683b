"""
Test providers for E2E testing.
These providers return test data instead of calling external APIs.
"""

import os
import base64
import json
from pathlib import Path
from typing import Dict, List, Optional, Any

from modules.media.providers.base import ImageProvider, TextProvider, VideoProvider
from modules.media.providers.config import ProviderConfig
from modules.media.schemas import ProviderMediaRequest, ProviderMediaResult


class TestImageProvider(ImageProvider):
    """Test image provider that returns test images."""

    def __init__(self):
        super().__init__()
        self.test_data_dir = Path(__file__).parent

    @property
    def provider_name(self) -> str:
        return "test_image"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize test provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate test images."""
        try:
            # Read test images from test_data directory
            test_images = []
            for i in range(min(request.num_images, 2)):  # Limit to available test images
                image_file = self.test_data_dir / f"banana_image_{i}.png"
                if image_file.exists():
                    # Read image file and encode as base64
                    with open(image_file, "rb") as f:
                        image_data = f.read()
                    base64_data = base64.b64encode(image_data).decode('utf-8')
                    data_url = f"data:image/png;base64,{base64_data}"

                    test_images.append({
                        "image_url": data_url,
                        "thumbnail_url": data_url,
                        "width": 1024,
                        "height": 1024,
                        "style": "test_style",
                        "variant_name": f"test_variant_{i}",
                        "prompt_used": f"Test prompt for {request.product_title}",
                        "generation_metadata": {
                            "provider": "test_image",
                            "model": "test_model",
                            "style_type": "test",
                            "target_audience": ["test"],
                            "usage_context": ["test"]
                        }
                    })

            return ProviderMediaResult(
                success=True,
                provider_job_id="test_image_job_123",
                images=test_images,
                estimated_completion_time=5,
                quality_score=0.9
            )

        except Exception as e:
            return ProviderMediaResult(
                success=False,
                error_message=f"Test image generation failed: {str(e)}"
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download test media."""
        if media_url.startswith("data:"):
            header, encoded = media_url.split(",", 1)
            return base64.b64decode(encoded)
        else:
            # For file paths
            with open(media_url, "rb") as f:
                return f.read()


class TestTextProvider(TextProvider):
    """Test text provider that returns test text content."""

    def __init__(self):
        super().__init__()
        self.test_data_dir = Path(__file__).parent

    @property
    def provider_name(self) -> str:
        return "test_text"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize test provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate test text content."""
        try:
            # Read test text files
            test_texts = []
            text_files = [
                "gemini_text_product_description_0.txt",
                "gemini_text_marketing_copy_1.txt",
                "gemini_text_seo_snippet_3.txt",
                "gemini_text_social_caption_2.txt"
            ]

            for i, filename in enumerate(text_files[:request.num_variants]):
                text_file = self.test_data_dir / filename
                if text_file.exists():
                    with open(text_file, "r", encoding="utf-8") as f:
                        content = f.read().strip()

                    # Determine content type from filename
                    if "product_description" in filename:
                        content_type = "product_description"
                    elif "marketing_copy" in filename:
                        content_type = "marketing_copy"
                    elif "seo_snippet" in filename:
                        content_type = "seo_snippet"
                    elif "social_caption" in filename:
                        content_type = "social_caption"
                    else:
                        content_type = "text"

                    test_texts.append({
                        "content_type": content_type,
                        "text": content,
                        "word_count": len(content.split()),
                        "character_count": len(content),
                        "variant_name": f"{content_type}_test_variant_{i}",
                        "language": "en",
                        "prompt_used": f"Test prompt for {request.product_title}"
                    })

            return ProviderMediaResult(
                success=True,
                provider_job_id="test_text_job_456",
                variants=test_texts,
                estimated_completion_time=2,
                quality_score=0.95
            )

        except Exception as e:
            return ProviderMediaResult(
                success=False,
                error_message=f"Test text generation failed: {str(e)}"
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download test media."""
        if media_url.startswith("http"):
            # Mock download
            return b"test content"
        else:
            with open(media_url, "rb") as f:
                return f.read()


class TestVideoProvider(VideoProvider):
    """Test video provider that returns test videos."""

    def __init__(self):
        super().__init__()
        self.test_data_dir = Path(__file__).parent

    @property
    def provider_name(self) -> str:
        return "test_video"

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize test provider - always succeeds."""
        self.config = config
        return True

    async def generate_media(self, request: ProviderMediaRequest) -> ProviderMediaResult:
        """Generate test videos."""
        try:
            # Read test video
            test_variants = []
            video_file = self.test_data_dir / "veo3_video_0_with_image.mp4"

            if video_file.exists():
                # For test purposes, we'll use a data URL or file path
                video_url = str(video_file)

                test_variants.append({
                    "variant_name": "test_video_variant_0",
                    "video_url": video_url,
                    "thumbnail_url": video_url.replace('.mp4', '_thumb.jpg'),  # Placeholder
                    "duration": 8.0,
                    "resolution": "1920x1080",
                    "aspect_ratio": "16:9",
                    "style_type": "test_showcase",
                    "prompt_used": f"Test video prompt for {request.product_title}",
                    "generation_metadata": {
                        "provider": "test_video",
                        "model": "test_model",
                        "style_type": "product_showcase",
                        "target_audience": ["test"],
                        "platform_optimized": "web"
                    }
                })

            return ProviderMediaResult(
                success=True,
                provider_job_id="test_video_job_789",
                variants=test_variants,
                estimated_completion_time=10,
                quality_score=0.88
            )

        except Exception as e:
            return ProviderMediaResult(
                success=False,
                error_message=f"Test video generation failed: {str(e)}"
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download test media."""
        if media_url.startswith("/") or media_url.startswith("./"):
            with open(media_url, "rb") as f:
                return f.read()
        else:
            # Mock download for URLs
            return b"test video content"

    def _get_resolution_for_aspect(self, aspect_ratio: str) -> str:
        """Get resolution for aspect ratio."""
        resolution_map = {
            "1:1": "1024x1024",
            "16:9": "1920x1080",
            "9:16": "1080x1920",
            "4:5": "1024x1280",
            "3:4": "1024x1365"
        }
        return resolution_map.get(aspect_ratio, "1920x1080")