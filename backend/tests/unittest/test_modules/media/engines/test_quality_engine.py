"""
Tests for quality engine.
Tests content validation and quality assessment functionality.
"""

import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from modules.media.engines.quality_engine import ProfessionalQualityEngine
from modules.media.schemas import ProductContext, ProductCategory, TargetAudience, ContentStyle


class TestQualityEngine:
    """Test QualityEngine functionality."""
    
    @pytest.fixture
    def quality_engine(self):
        """Create a quality engine instance for testing."""
        return ProfessionalQualityEngine()
    
    @pytest.fixture
    def sample_product_context(self):
        """Create sample product context for testing."""
        return ProductContext(
            title="Premium Wireless Headphones",
            description="High-quality wireless headphones with noise cancellation",
            category=ProductCategory.ELECTRONICS,
            price=199.99,
            currency="USD",
            brand="AudioTech",
            target_audience=[TargetAudience.PROFESSIONALS],
            key_features=["noise cancellation", "wireless", "long battery"],
            style_preferences=ContentStyle.PROFESSIONAL
        )
    
    @pytest.fixture
    def sample_image_data(self):
        """Create sample image data for testing."""
        return {
            "url": "https://example.com/headphones.jpg",
            "width": 1024,
            "height": 1024,
            "format": "jpeg",
            "file_size": 256000,  # 256KB
            "metadata": {
                "color_profile": "sRGB",
                "compression": "high"
            }
        }
    
    @pytest.fixture
    def sample_text_content(self):
        """Create sample text content for testing."""
        return "Experience premium audio quality with our wireless headphones featuring advanced noise cancellation technology."
    
    @pytest.mark.asyncio
    async def test_assess_image_quality(self, quality_engine, sample_image_data, sample_product_context):
        """Test image quality assessment."""
        result = await quality_engine.assess_image_quality(
            sample_image_data,
            sample_product_context
        )

        assert isinstance(result, dict)
        assert "overall_score" in result
        assert "technical_quality" in result
        assert "content_relevance" in result
        assert "brand_alignment" in result
        assert "recommendations" in result

        # Overall score should be reasonable
        assert 0 <= result["overall_score"] <= 100
        assert result["overall_score"] > 70  # Should be good quality
    
    @pytest.mark.asyncio
    async def test_assess_video_quality(self, quality_engine, sample_product_context):
        """Test video quality assessment."""
        video_data = {
            "url": "https://example.com/headphones_video.mp4",
            "duration": 30,
            "width": 1920,
            "height": 1080,
            "fps": 30,
            "bitrate": 5000000,  # 5 Mbps
            "format": "mp4",
            "codec": "h264"
        }

        result = await quality_engine.assess_video_quality(
            video_data,
            sample_product_context
        )

        assert isinstance(result, dict)
        assert "overall_score" in result
        assert "technical_quality" in result
        assert "content_relevance" in result
        assert "engagement_potential" in result
        assert "recommendations" in result

        assert 0 <= result["overall_score"] <= 100
    
    @pytest.mark.asyncio
    async def test_assess_text_quality(self, quality_engine, sample_text_content, sample_product_context):
        """Test text quality assessment."""
        result = await quality_engine.assess_text_quality(
            sample_text_content, 
            sample_product_context
        )
        
        assert isinstance(result, dict)
        assert "overall_score" in result
        assert "readability" in result
        assert "relevance" in result
        assert "engagement" in result
        assert "seo_potential" in result
        assert "recommendations" in result
        
        # Should score well for relevant, well-written text
        assert 0 <= result["overall_score"] <= 100
        assert result["overall_score"] > 60
    
    @pytest.mark.asyncio
    async def test_check_brand_alignment(self, quality_engine, sample_product_context):
        """Test brand alignment checking."""
        content_data = {
            "text": "Premium wireless headphones with professional audio quality",
            "visual_elements": ["sleek design", "black color", "modern style"],
            "tone": "professional"
        }
        
        alignment_score = await quality_engine.check_brand_alignment(
            content_data, 
            sample_product_context
        )
        
        assert isinstance(alignment_score, (int, float))
        assert 0 <= alignment_score <= 100
        
        # Should align well with professional brand
        assert alignment_score > 70
    
    @pytest.mark.asyncio
    async def test_validate_content_guidelines(self, quality_engine, sample_product_context):
        """Test content guidelines validation."""
        content = {
            "type": "image",
            "url": "https://example.com/image.jpg",
            "alt_text": "Premium wireless headphones",
            "metadata": {
                "width": 1024,
                "height": 1024,
                "format": "jpeg"
            }
        }
        
        validation_result = await quality_engine.validate_content_guidelines(
            content, 
            sample_product_context
        )
        
        assert isinstance(validation_result, dict)
        assert "is_valid" in validation_result
        assert "violations" in validation_result
        assert "warnings" in validation_result
        assert "score" in validation_result
        
        # Should be valid content
        assert validation_result["is_valid"] is True
        assert len(validation_result["violations"]) == 0
    
    @pytest.mark.asyncio
    async def test_generate_quality_recommendations(self, quality_engine, sample_product_context):
        """Test quality recommendations generation."""
        quality_metrics = {
            "overall_score": 75,
            "technical_quality": 80,
            "content_relevance": 70,
            "brand_alignment": 75,
            "areas_for_improvement": ["lighting", "composition"]
        }
        
        recommendations = await quality_engine.generate_quality_recommendations(
            quality_metrics, 
            sample_product_context
        )
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        
        # Should provide actionable recommendations
        recommendations_text = " ".join(recommendations).lower()
        assert any(term in recommendations_text for term in ["improve", "enhance", "adjust", "optimize"])
    
    @pytest.mark.asyncio
    async def test_quality_assessment_different_categories(self, quality_engine):
        """Test quality assessment for different product categories."""
        # Fashion product
        fashion_context = ProductContext(
            title="Designer Dress",
            description="Elegant evening dress",
            category=ProductCategory.FASHION_APPAREL,
            price=299.99,
            currency="USD",
            target_audience=[],
            style_preferences=ContentStyle.LUXURY
        )
        
        fashion_text = "Elegant designer dress perfect for special occasions"
        fashion_result = await quality_engine.assess_text_quality(fashion_text, fashion_context)
        
        # Electronics product
        electronics_context = ProductContext(
            title="Smartphone",
            description="Latest smartphone with advanced features",
            category=ProductCategory.ELECTRONICS,
            price=899.99,
            currency="USD",
            target_audience=[],
            style_preferences=ContentStyle.MODERN
        )
        
        electronics_text = "Advanced smartphone with cutting-edge technology"
        electronics_result = await quality_engine.assess_text_quality(electronics_text, electronics_context)
        
        # Both should have valid assessments but different criteria
        assert fashion_result["overall_score"] > 0
        assert electronics_result["overall_score"] > 0
        assert fashion_result["recommendations"] != electronics_result["recommendations"]
    
    @pytest.mark.asyncio
    async def test_quality_thresholds(self, quality_engine, sample_product_context):
        """Test quality threshold validation."""
        # High quality content
        high_quality_metrics = {
            "overall_score": 95,
            "technical_quality": 98,
            "content_relevance": 92,
            "brand_alignment": 94
        }
        
        high_quality_result = await quality_engine.meets_quality_threshold(
            high_quality_metrics, 
            threshold=80
        )
        
        assert high_quality_result is True
        
        # Low quality content
        low_quality_metrics = {
            "overall_score": 65,
            "technical_quality": 60,
            "content_relevance": 70,
            "brand_alignment": 65
        }
        
        low_quality_result = await quality_engine.meets_quality_threshold(
            low_quality_metrics, 
            threshold=80
        )
        
        assert low_quality_result is False
    
    @pytest.mark.asyncio
    async def test_comparative_quality_analysis(self, quality_engine, sample_product_context):
        """Test comparative quality analysis between variants."""
        variant1_data = {
            "type": "image",
            "url": "https://example.com/variant1.jpg",
            "quality_score": 85
        }
        
        variant2_data = {
            "type": "image",
            "url": "https://example.com/variant2.jpg",
            "quality_score": 78
        }
        
        comparison = await quality_engine.compare_variants(
            [variant1_data, variant2_data], 
            sample_product_context
        )
        
        assert isinstance(comparison, dict)
        assert "best_variant" in comparison
        assert "ranking" in comparison
        assert "comparison_metrics" in comparison
        
        # Should identify the higher quality variant
        assert comparison["best_variant"]["quality_score"] == 85
    
    @pytest.mark.asyncio
    async def test_quality_engine_error_handling(self, quality_engine, sample_product_context):
        """Test quality engine error handling."""
        # Test with invalid image data
        invalid_image_data = {
            "url": "invalid_url",
            "width": -1,  # Invalid width
            "height": 0   # Invalid height
        }
        
        result = await quality_engine.assess_image_quality(
            invalid_image_data, 
            sample_product_context
        )
        
        # Should handle errors gracefully
        assert isinstance(result, dict)
        assert "overall_score" in result
        assert result["overall_score"] >= 0  # Should not crash
    
    def test_quality_engine_initialization(self, quality_engine):
        """Test quality engine initialization."""
        assert quality_engine is not None
        assert hasattr(quality_engine, 'assess_image_quality')
        assert hasattr(quality_engine, 'assess_video_quality')
        assert hasattr(quality_engine, 'assess_text_quality')
        assert hasattr(quality_engine, 'check_brand_alignment')
        assert hasattr(quality_engine, 'validate_content_guidelines')
        assert hasattr(quality_engine, 'generate_quality_recommendations')
