"""rename_plan_column_to_plan_tier_in_tenants

Revision ID: 96a76193222c
Revises: 178467a94759
Create Date: 2025-09-16 02:04:31.719868

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '96a76193222c'
down_revision: Union[str, Sequence[str], None] = '178467a94759'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Rename 'plan' column to 'plan_tier' in tenants table
    op.alter_column('tenants', 'plan', new_column_name='plan_tier')


def downgrade() -> None:
    """Downgrade schema."""
    # Rename 'plan_tier' column back to 'plan' in tenants table
    op.alter_column('tenants', 'plan_tier', new_column_name='plan')
