"""
End-to-End tests for Media Push APIs.

Tests the complete workflow of pushing generated media to platforms.
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from unittest.mock import patch, MagicMock

from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.stores.models import Store


class TestMediaPushE2E:
    """End-to-end tests for media push workflow."""

    @pytest.mark.asyncio
    async def test_push_media_to_shopify_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test complete workflow of pushing media to Shopify."""
        
        # Step 1: Generate media first
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200
        
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, db_job.id)
        assert completed_job["status"] == "completed"

        # Get a variant ID
        variant_id = completed_job["variants"][0]["variant_id"]
        product_id = db_job.product_id
        
        # Step 2: Mock Shopify API calls
        with patch("modules.stores.shopify_service.ShopifyService.upload_product_media") as mock_upload:
            mock_upload.return_value = {
                "success": True,
                "media_id": "shopify_media_123",
                "url": "https://cdn.shopify.com/media/123.png"
            }
            
            # Step 3: Push media to Shopify
            push_request = {
                "shop_id": test_store.id,
                "product_id": int(product_id),
                "variant_id": variant_id,
                "publish_targets": ["shopify"],
                "publish_options": {
                    "alt_text": "Premium wireless headphones",
                    "position": 1,
                    "replace_existing": False
                }
            }
            
            push_response = await e2e_client.post("/api/media/push", json=push_request)
            assert push_response.status_code == 200
            
            push_data = push_response.json()
            assert push_data["success"] is True
            assert push_data["status"] == "completed"
            assert "push_id" in push_data
            
            # Verify Shopify service was called
            mock_upload.assert_called_once()

    @pytest.mark.asyncio
    async def test_push_media_unauthorized_store(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store
    ):
        """Test push request with unauthorized store access."""
        
        # Try to push to a store that doesn't belong to the user
        push_request = {
            "shop_id": 99999,  # Non-existent store ID
            "product_id": 123,
            "variant_id": 456,
            "publish_targets": ["shopify"]
        }
        
        response = await e2e_client.post("/api/media/push", json=push_request)
        assert response.status_code == 403
        assert "Access denied" in response.json()["detail"]

    @pytest.mark.asyncio
    async def test_push_media_multiple_platforms(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test pushing media to multiple platforms."""
        
        # Generate media first
        request_data = sample_media_requests["video_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        
        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, db_job.id)
        variant_id = completed_job["variants"][0]["variant_id"]
        
        # Mock multiple platform services
        with patch("modules.stores.shopify_service.ShopifyService.upload_product_media") as mock_shopify, \
             patch("modules.social.tiktok_service.TikTokService.upload_video") as mock_tiktok:
            
            mock_shopify.return_value = {"success": True, "media_id": "shopify_123"}
            mock_tiktok.return_value = {"success": True, "video_id": "tiktok_456"}
            
            # Push to multiple platforms
            push_request = {
                "shop_id": test_store.id,
                "product_id": int(db_job.product_id),
                "variant_id": variant_id,
                "publish_targets": ["shopify", "tiktok"],
                "publish_options": {
                    "alt_text": "Running shoes video",
                    "replace_existing": True
                }
            }
            
            response = await e2e_client.post("/api/media/push", json=push_request)
            assert response.status_code == 200
            
            push_data = response.json()
            assert push_data["success"] is True
            
            # Verify both services were called
            mock_shopify.assert_called_once()
            # Note: TikTok mock might not be called if not implemented yet

    @pytest.mark.asyncio
    async def test_push_media_with_custom_options(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test push with custom publishing options."""
        
        # Generate media first
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, db_job.id)
        variant_id = completed_job["variants"][0]["variant_id"]

        # Mock Shopify service
        with patch("modules.stores.shopify_service.ShopifyService.upload_product_media") as mock_upload:
            mock_upload.return_value = {"success": True, "media_id": "shopify_789"}

            # Push with detailed options
            push_request = {
                "shop_id": test_store.id,
                "product_id": int(db_job.product_id),
                "variant_id": variant_id,
                "publish_targets": ["shopify"],
                "publish_options": {
                    "alt_text": "Premium wireless headphones in black and white",
                    "position": 2,
                    "replace_existing": True
                }
            }
            
            response = await e2e_client.post("/api/media/push", json=push_request)
            assert response.status_code == 200
            
            # Verify the options were passed correctly
            call_args = mock_upload.call_args
            assert call_args is not None
            
            # Check that publish options were included in the call
            # The exact structure depends on the service implementation

    @pytest.mark.asyncio
    async def test_push_nonexistent_variant(
        self,
        e2e_client: AsyncClient,
        test_store: Store
    ):
        """Test push request with non-existent variant."""
        
        push_request = {
            "shop_id": test_store.id,
            "product_id": 123,
            "variant_id": 99999,  # Non-existent variant
            "publish_targets": ["shopify"]
        }
        
        response = await e2e_client.post("/api/media/push", json=push_request)
        # Should return 404 or 400 depending on implementation
        assert response.status_code in [400, 404, 500]

    @pytest.mark.asyncio
    async def test_push_media_platform_failure(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test push workflow when platform upload fails."""
        
        # Generate media first
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, db_job.id)
        variant_id = completed_job["variants"][0]["variant_id"]

        # Mock Shopify service to fail
        with patch("modules.stores.shopify_service.ShopifyService.upload_product_media") as mock_upload:
            mock_upload.side_effect = Exception("Shopify API error")

            push_request = {
                "shop_id": test_store.id,
                "product_id": int(db_job.product_id),
                "variant_id": variant_id,
                "publish_targets": ["shopify"]
            }
            
            response = await e2e_client.post("/api/media/push", json=push_request)
            
            # Should handle the error gracefully
            assert response.status_code in [200, 500]
            
            if response.status_code == 200:
                push_data = response.json()
                assert push_data["success"] is False
                assert push_data["status"] == "failed"
