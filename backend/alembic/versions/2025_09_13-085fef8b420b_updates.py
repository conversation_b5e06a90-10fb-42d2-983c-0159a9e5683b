"""updates

Revision ID: 085fef8b420b
Revises: 5cfa84fe0009
Create Date: 2025-09-13 00:18:53.069255

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '085fef8b420b'
down_revision: Union[str, Sequence[str], None] = '5cfa84fe0009'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('media_reviews',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('variant_id', sa.Integer(), nullable=False),
    sa.Column('reviewer_id', sa.Integer(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('quality_score', sa.Float(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.Column('rejection_reasons', sa.JSON(), nullable=True),
    sa.Column('suggested_improvements', sa.Text(), nullable=True),
    sa.Column('review_criteria', sa.JSON(), nullable=True),
    sa.Column('review_time_seconds', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['reviewer_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['variant_id'], ['media_variants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_reviews_id'), 'media_reviews', ['id'], unique=False)
    op.create_table('rejected_assets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('original_variant_id', sa.Integer(), nullable=False),
    sa.Column('job_id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('rejection_reason', sa.String(), nullable=False),
    sa.Column('rejection_category', sa.String(), nullable=True),
    sa.Column('rejection_details', sa.JSON(), nullable=True),
    sa.Column('original_media_url', sa.String(), nullable=True),
    sa.Column('original_prompt', sa.Text(), nullable=True),
    sa.Column('original_settings', sa.JSON(), nullable=True),
    sa.Column('regeneration_attempts', sa.Integer(), nullable=True),
    sa.Column('last_regeneration_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('regenerated_variant_id', sa.Integer(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['media_jobs.id'], ),
    sa.ForeignKeyConstraint(['original_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['regenerated_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_rejected_assets_id'), 'rejected_assets', ['id'], unique=False)
    op.drop_index(op.f('ix_voices_id'), table_name='voices')
    op.drop_table('voices')
    op.alter_column('generated_assets', 'product_id',
               existing_type=sa.VARCHAR(),
               type_=sa.Integer(),
               existing_nullable=False,
               postgresql_using='product_id::integer')
    op.alter_column('generation_requests', 'product_id',
               existing_type=sa.VARCHAR(),
               type_=sa.Integer(),
               existing_nullable=False,
               postgresql_using='product_id::integer')
    op.add_column('media_jobs', sa.Column('mode', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('model', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('settings', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('items', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('shop_id', sa.Integer(), nullable=True))
    op.add_column('media_jobs', sa.Column('product_ids', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('celery_task_id', sa.String(), nullable=True))
    op.add_column('media_jobs', sa.Column('idempotency_key', sa.String(length=32), nullable=True))
    op.add_column('media_jobs', sa.Column('product_version', sa.String(length=16), nullable=True))
    op.add_column('media_jobs', sa.Column('needs_manual_review', sa.Boolean(), nullable=True))
    op.add_column('media_jobs', sa.Column('qa_metadata', sa.JSON(), nullable=True))
    op.add_column('media_jobs', sa.Column('language', sa.String(length=10), nullable=True))
    op.add_column('media_jobs', sa.Column('fallback_language', sa.String(length=10), nullable=True))
    op.alter_column('media_jobs', 'product_id',
               existing_type=sa.VARCHAR(),
               type_=sa.Integer(),
               existing_nullable=False,
               postgresql_using='product_id::integer')
    op.create_index(op.f('ix_media_jobs_idempotency_key'), 'media_jobs', ['idempotency_key'], unique=False)
    op.drop_column('media_jobs', 'script')
    op.drop_column('media_jobs', 'voice_id')
    op.add_column('media_variants', sa.Column('captions', sa.Text(), nullable=True))
    op.add_column('media_variants', sa.Column('text_content', sa.Text(), nullable=True))
    op.add_column('media_variants', sa.Column('quality_score', sa.Float(), nullable=True))
    op.add_column('media_variants', sa.Column('needs_manual_review', sa.Boolean(), nullable=True))
    op.add_column('media_variants', sa.Column('qa_metadata', sa.JSON(), nullable=True))
    op.add_column('media_variants', sa.Column('brand_safety_checked', sa.Boolean(), nullable=True))
    op.add_column('media_variants', sa.Column('copyright_validated', sa.Boolean(), nullable=True))
    op.add_column('media_variants', sa.Column('content_flags', sa.JSON(), nullable=True))
    op.alter_column('media_variants', 'alt_text',
               existing_type=sa.VARCHAR(),
               type_=sa.Text(),
               existing_nullable=True)
    op.drop_column('media_variants', 'voice_url')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('media_variants', sa.Column('voice_url', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.alter_column('media_variants', 'alt_text',
               existing_type=sa.Text(),
               type_=sa.VARCHAR(),
               existing_nullable=True)
    op.drop_column('media_variants', 'content_flags')
    op.drop_column('media_variants', 'copyright_validated')
    op.drop_column('media_variants', 'brand_safety_checked')
    op.drop_column('media_variants', 'qa_metadata')
    op.drop_column('media_variants', 'needs_manual_review')
    op.drop_column('media_variants', 'quality_score')
    op.drop_column('media_variants', 'text_content')
    op.drop_column('media_variants', 'captions')
    op.add_column('media_jobs', sa.Column('voice_id', sa.VARCHAR(), autoincrement=False, nullable=True))
    op.add_column('media_jobs', sa.Column('script', sa.TEXT(), autoincrement=False, nullable=True))
    op.drop_index(op.f('ix_media_jobs_idempotency_key'), table_name='media_jobs')
    op.alter_column('media_jobs', 'product_id',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.drop_column('media_jobs', 'fallback_language')
    op.drop_column('media_jobs', 'language')
    op.drop_column('media_jobs', 'qa_metadata')
    op.drop_column('media_jobs', 'needs_manual_review')
    op.drop_column('media_jobs', 'product_version')
    op.drop_column('media_jobs', 'idempotency_key')
    op.drop_column('media_jobs', 'celery_task_id')
    op.drop_column('media_jobs', 'product_ids')
    op.drop_column('media_jobs', 'shop_id')
    op.drop_column('media_jobs', 'items')
    op.drop_column('media_jobs', 'settings')
    op.drop_column('media_jobs', 'model')
    op.drop_column('media_jobs', 'mode')
    op.alter_column('generation_requests', 'product_id',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.alter_column('generated_assets', 'product_id',
               existing_type=sa.Integer(),
               type_=sa.VARCHAR(),
               existing_nullable=False)
    op.create_table('voices',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('provider', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('provider_voice_id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('gender', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('accent', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('language', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('sample_url', sa.VARCHAR(), autoincrement=False, nullable=True),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True),
    sa.Column('plan_tier_required', postgresql.ENUM('FREE', 'STARTER', 'GROWTH', 'PRO', 'ENTERPRISE', name='plan_tier'), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True),
    sa.PrimaryKeyConstraint('id', name=op.f('voices_pkey'))
    )
    op.create_index(op.f('ix_voices_id'), 'voices', ['id'], unique=False)
    op.drop_index(op.f('ix_rejected_assets_id'), table_name='rejected_assets')
    op.drop_table('rejected_assets')
    op.drop_index(op.f('ix_media_reviews_id'), table_name='media_reviews')
    op.drop_table('media_reviews')
    # ### end Alembic commands ###
