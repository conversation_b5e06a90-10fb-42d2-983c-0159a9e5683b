#!/usr/bin/env python3
"""
Test script to demonstrate provider selection based on request model field.
This shows how users can now specify providers directly in their API requests.
"""

import asyncio
import sys
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from modules.media.service import MediaGenerationService
from modules.media.schemas import MediaGenerateRequest, ProductItem


async def test_provider_selection():
    """Test provider selection with different model specifications."""
    print("🧪 Testing Provider Selection with Model Field")
    print("=" * 60)

    service = MediaGenerationService()

    # Test 1: Request with specific model (should use that model)
    print("\n📋 Test 1: Request with specific model 'example_image'")
    request_with_model = MediaGenerateRequest(
        mode="image",
        media_type="image",
        model="example_image",  # This should be used directly
        items=[
            ProductItem(
                product_id="123",
                prompt="Test product for example provider"
            )
        ]
    )

    # Provider selection now happens in the manager with override checking
    # Simulate getting the provider directly from manager
    from modules.media.providers.manager import get_image_provider
    provider = await get_image_provider(request_with_model.model)
    provider_name = provider.provider_name
    print(f"✅ Selected provider: {provider_name}")
    assert provider_name == "example_image", f"Expected 'example_image', got '{provider_name}'"

    # Test 2: Request without model (should raise error - no defaults)
    print("\n📋 Test 2: Request without model (raises error - no defaults)")
    request_without_model = MediaGenerateRequest(
        mode="image",
        media_type="image",
        # No model specified - should raise error
        items=[
            ProductItem(
                product_id="456",
                prompt="Test product for default provider"
            )
        ]
    )

    # Test without model - should use the requested model directly
    # Since override checking is in manager, we test the manager behavior
    try:
        from modules.media.providers.manager import get_image_provider
        # This will fail because no model is specified
        provider = await get_image_provider(None)
        print(f"❌ Expected error but got provider: {provider.provider_name}")
        assert False, "Should have raised ValueError"
    except (ValueError, TypeError) as e:
        print(f"✅ Correctly raised error: {e}")
        assert "not found" in str(e).lower() or "None" in str(e)

    # Test 3: Request with different media types
    print("\n📋 Test 3: Different media types with specific models")

    # Video with example provider
    video_request = MediaGenerateRequest(
        mode="video",
        media_type="video",
        model="example_video",
        items=[ProductItem(product_id="789", prompt="Test video")]
    )

    # Video with example provider
    from modules.media.providers.manager import get_video_provider
    video_provider_instance = await get_video_provider(video_request.model)
    video_provider = video_provider_instance.provider_name
    print(f"✅ Video provider: {video_provider}")
    assert video_provider == "example_video"

    # Text with example provider
    text_request = MediaGenerateRequest(
        mode="text",
        media_type="text",
        model="example_text",
        items=[ProductItem(product_id="101", prompt="Test text")]
    )

    from modules.media.providers.manager import get_text_provider
    text_provider_instance = await get_text_provider(text_request.model)
    text_provider = text_provider_instance.provider_name
    print(f"✅ Text provider: {text_provider}")
    assert text_provider == "example_text"

    print("\n" + "=" * 60)
    print("🎉 Provider Selection Tests Passed!")
    print("✅ Users can specify providers directly in API requests")
    print("🔧 *_PROVIDER_OVERRIDE environment variables take highest precedence")
    print("⚡ When override is set, it ALWAYS uses that provider (with logging)")
    print("🚫 No defaults or fallbacks - explicit specification required")
    print("=" * 60)


async def demonstrate_api_usage():
    """Show example API usage with provider selection."""
    print("\n📚 API Usage Examples:")
    print("=" * 40)

    examples = [
        {
            "description": "Use example provider for testing",
            "request": {
                "mode": "image",
                "model": "example_image",
                "items": [{"product_id": "123"}]
            }
        },
        {
            "description": "Use production provider",
            "request": {
                "mode": "image",
                "model": "banana",
                "items": [{"product_id": "456"}]
            }
        },
        {
            "description": "Provider override takes precedence (env var set)",
            "request": {
                "mode": "image",
                "model": "example_image",
                "items": [{"product_id": "789"}]
            },
            "note": "If IMAGE_PROVIDER_OVERRIDE=forced_provider, it will use 'forced_provider' instead"
        },
        {
            "description": "Error when no provider specified",
            "request": {
                "mode": "image",
                "items": [{"product_id": "999"}]
            },
            "note": "Will raise error: No provider specified for image generation"
        }
    ]

    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['description']}:")
        print(f"   POST /api/media/generate")
        print(f"   {example['request']}")

        if 'note' in example:
            print(f"   📝 Note: {example['note']}")

        # Simulate provider selection
        request = MediaGenerateRequest(**example['request'])
        service = MediaGenerationService()

        try:
            # Simulate provider selection through manager
            media_type = request.media_type or request.mode
            if media_type == "image":
                from modules.media.providers.manager import get_image_provider
                provider_instance = await get_image_provider(request.model)
            elif media_type == "video":
                from modules.media.providers.manager import get_video_provider
                provider_instance = await get_video_provider(request.model)
            elif media_type == "text":
                from modules.media.providers.manager import get_text_provider
                provider_instance = await get_text_provider(request.model)
            else:
                raise ValueError(f"Unsupported media type: {media_type}")

            provider = provider_instance.provider_name
            print(f"   → Uses provider: {provider}")
        except (ValueError, TypeError) as e:
            print(f"   → Error: {e}")


if __name__ == "__main__":
    asyncio.run(test_provider_selection())
    asyncio.run(demonstrate_api_usage())