{"task_routes": {"sync.bulk_sync_store": {"queue": "bulk-sync"}, "sync.check_sync_status": {"queue": "sync-status"}, "sync.monitor_scheduled_syncs": {"queue": "sync-monitoring"}, "sync.cleanup_old_sync_jobs": {"queue": "sync-cleanup"}, "webhook.process_webhook_event": {"queue": "webhook-processing"}, "webhook.cleanup_old_webhook_events": {"queue": "webhook-cleanup"}, "webhook.retry_failed_webhooks": {"queue": "webhook-retry"}, "media.generate_product_video": {"queue": "media-generation"}, "media.generate_product_images": {"queue": "media-generation"}, "media.generate_media": {"queue": "media-generation"}, "media.push_media_to_shopify": {"queue": "media-push"}, "media.batch_generate_videos": {"queue": "media-batch"}, "media.cleanup_old_media": {"queue": "media-cleanup"}, "system.health_check": {"queue": "system"}, "deprecated.*": {"queue": "deprecated"}}, "task_default_queue": "celery", "task_default_exchange": "celery", "task_default_exchange_type": "direct", "task_default_routing_key": "celery", "worker_prefetch_multiplier": 1, "task_acks_late": true, "worker_max_tasks_per_child": 1000, "worker_disable_rate_limits": false, "worker_send_task_events": true, "task_send_sent_event": true, "result_expires": 3600, "result_backend_transport_options": {"retry_policy": {"timeout": 5.0}}, "broker_transport_options": {"retry_policy": {"timeout": 5.0}}, "beat_schedule": {"monitor-scheduled-syncs": {"task": "sync.monitor_scheduled_syncs", "schedule": 10.0, "options": {"queue": "sync-monitoring"}}, "cleanup-old-sync-jobs": {"task": "sync.cleanup_old_sync_jobs", "schedule": 86400.0, "options": {"queue": "sync-cleanup"}}, "cleanup-old-webhook-events": {"task": "webhook.cleanup_old_webhook_events", "schedule": 86400.0, "options": {"queue": "webhook-cleanup"}}, "retry-failed-webhooks": {"task": "webhook.retry_failed_webhooks", "schedule": 300.0, "options": {"queue": "webhook-retry"}}}, "defaults": {"sync_check_interval_minutes": 30, "sync_cleanup_days_old": 30}, "platforms": {"shopify": {"entity_types": ["products", "product_variants", "product_images", "inventory_levels", "metafield_products", "metafield_product_variants", "metafield_product_images"], "batch_size": 1000, "sync_settings": {"max_retries": 3, "timeout_seconds": 300, "rate_limit_delay": 1.0}}}}