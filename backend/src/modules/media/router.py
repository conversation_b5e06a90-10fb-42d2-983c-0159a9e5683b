"""
Media Generation API Router
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from core.db.database import get_db

from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from modules.queue.queue_service import celery_service as job_queue_service, TaskPriority
from .models import (
    MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
)
from .schemas import (
    MediaGenerateRequest, MediaGenerateResponse, MediaJobStatusResponse,
    MediaPushRequest, MediaPushResponse, MediaJobListResponse,
    MediaVariantInfo, MediaJobInfo
)
from .service import media_service
from modules.stores.models import Store

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/generate", response_model=MediaGenerateResponse)
async def generate_videos(
    request: MediaGenerateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate 4 video variants for selected products.

    Body: {shopId, productIds[], templateId?, voiceId?, aspectRatio?, locale?}
    Returns: job IDs per product-variant
    """
    try:
        # Get user's tenant
        tenant_result = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant_result.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Validate shop ownership
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if hasattr(request, 'shop_id') and request.shop_id:
            if request.shop_id not in store_ids:
                raise HTTPException(
                    status_code=403,
                    detail="Access denied: shop does not belong to user"
                )

        # Create video generation jobs with full payload
        jobs = await media_service.create_generation_jobs(
            db=db,
            user_id=current_user.id,  # Use user ID directly
            request=request
        )

        # Check quota and budget before queuing tasks
        from modules.billing.quota_service import quota_service
        media_type = request.media_type or request.mode or "image"
        total_items = sum(len(job.full_payload.get("items", [])) for job in jobs)

        has_quota, quota_info = await quota_service.check_quota(current_user.id, media_type, total_items)
        if not has_quota:
            logger.error(f"Quota check failed for user {current_user.id}: has_quota={has_quota}, quota_info={quota_info}")
            quota_type = quota_info.get('quota_type', 'unknown')
            logger.error(f"Extracted quota_type: {quota_type}")
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail=f"Quota exceeded: {quota_type} limit reached"
            )

        has_budget, budget_info = await quota_service.check_budget(current_user.id, media_type, total_items)
        if not has_budget:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail=f"Budget exceeded: {budget_info.get('budget_type', 'insufficient funds')}"
            )

        # Deduct quota and budget immediately since we've validated availability
        # This ensures billing happens even if the async worker has issues
        deduction_success = await quota_service.deduct_quota_and_budget(current_user.id, media_type, total_items)
        if not deduction_success:
            logger.error(f"Failed to deduct quota/budget for user {current_user.id} after validation")
            # Continue anyway since we already validated availability

        # Queue generation tasks using Celery
        from modules.queue.queue_service import celery_service

        job_responses = []
        for job in jobs:
            # Enqueue the task using Celery
            celery_task_id = celery_service.enqueue_media_generation(
                user_id=current_user.id,
                job_id=job.id,
                product_ids=[job.product_id],
                media_type=job.media_type,
                template_id=job.template_id,
                voice_id=job.voice_id,
                text_input=job.script,
                full_payload=job.full_payload  # Pass the complete payload
            )

            job_responses.append({
                "product_id": job.product_id,
                "job_id": job.id, # Use the internal DB ID
                "celery_task_id": celery_task_id,
                "status": job.status.value
            })

        return MediaGenerateResponse(jobs=job_responses)
        
    except Exception as e:
        logger.exception(f"Error generating videos: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}", response_model=MediaJobStatusResponse)
async def get_job_status(
    job_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get job status, progress, and variant IDs.
    """
    try:
        job = await media_service.get_job_with_variants(db, job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Add ownership validation
        # Check if job belongs to current user
        if hasattr(job, 'user_id') and job.user_id != current_user.id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: job does not belong to user"
            )

        return MediaJobStatusResponse(
            job_id=job.id,
            status=job.status.value,
            progress=job.progress_percentage,
            variants=[{
                "variant_id": variant.id,
                "variant_name": variant.variant_name,
                "status": variant.status.value,
                "video_url": variant.video_url,
                "image_url": variant.image_url,
                "thumbnail_url": variant.thumbnail_url,
                "duration": variant.duration_seconds
            } for variant in job.variants] if hasattr(job, 'variants') else []
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}/status", response_model=MediaJobStatusResponse)
async def get_job_status_detailed(
    job_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get detailed job status with progress and variants.
    """
    try:
        status_data = await media_service.get_job_status(db, job_id, current_user.id)
        if not status_data:
            raise HTTPException(status_code=404, detail="Job not found")

        return MediaJobStatusResponse(**status_data)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/push", response_model=MediaPushResponse)
async def push_to_platform(
    request: MediaPushRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Push selected video variant to connected store's product media.

    Body: {shopId, productId, variantId, publishTargets, publishOptions}
    """
    try:
        # Validate store ownership and permissions
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if request.shop_id not in store_ids:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        # Find the store to determine platform
        store_result = await db.execute(select(Store).filter(
            Store.id == request.shop_id,
            Store.owner_id == current_user.id
        ))
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=404,
                detail="Store not found"
            )

        # Use the service method for pushing
        result = await media_service.push_media_to_platforms(
            db=db,
            job_id=request.variant_id,  # Assuming variant_id is actually job_id for now
            user_id=current_user.id,
            publish_targets=request.publish_targets,
            publish_options=request.publish_options
        )

        platform_name = store.platform.title()
        return MediaPushResponse(
            success=result["success"],
            results=result["results"],
            push_id=f"push_{request.variant_id}_{request.product_id}",
            status="completed" if result["success"] else "failed",
            message=f"Push to {platform_name} {'completed' if result['success'] else 'failed'}"
        )

    except Exception as e:
        logger.error(f"Error pushing to platform: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=MediaJobListResponse)
async def list_jobs(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    product_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List media generation jobs for the current user.

    Supports filtering by status and product ID, with pagination.
    """
    try:
        # Use the service method for consistency
        result = await media_service.list_user_jobs(
            db=db,
            user_id=current_user.id,
            page=page,
            per_page=per_page,
            status_filter=status_filter,
            product_id=product_id
        )

        # Return the jobs as dictionaries (MediaJobListResponse expects List[Dict[str, Any]])
        return MediaJobListResponse(
            jobs=result["jobs"],
            total=result["total"],
            page=result["page"],
            per_page=result["per_page"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}"
        )


@router.post("/jobs/{job_id}/cancel")
async def cancel_job(
    job_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Cancel a media generation job.
    """
    try:
        success = await media_service.cancel_job(db, job_id, current_user.id)
        if not success:
            raise HTTPException(status_code=404, detail="Job not found or cannot be cancelled")

        return {"message": "Job cancelled successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling job: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/jobs/{job_id}/retry")
async def retry_job(
    job_id: int,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retry a failed media generation job.
    """
    try:
        new_job = await media_service.retry_job(db, job_id, current_user.id)
        if not new_job:
            raise HTTPException(status_code=404, detail="Job not found or cannot be retried")

        return {
            "message": "Job retry initiated successfully",
            "new_job_id": new_job.id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrying job: {e}")
        raise HTTPException(status_code=500, detail=str(e))

