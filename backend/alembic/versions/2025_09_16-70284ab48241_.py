"""empty message

Revision ID: 70284ab48241
Revises: 251b1c081e5d, f7d15ac092a8
Create Date: 2025-09-16 00:07:27.082062

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '70284ab48241'
down_revision: Union[str, Sequence[str], None] = ('251b1c081e5d', 'f7d15ac092a8')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
