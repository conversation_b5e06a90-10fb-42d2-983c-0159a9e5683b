"""Add provider column to MediaVariant

Revision ID: 7115c89c38cd
Revises: 96a76193222c
Create Date: 2025-09-16 04:37:09.616255

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '7115c89c38cd'
down_revision: Union[str, Sequence[str], None] = '96a76193222c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('media_variants', sa.Column('provider', sa.String(), nullable=True))
    op.add_column('tenants', sa.Column('plan_tier', sa.String(length=50), nullable=True))
    op.drop_column('tenants', 'plan')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('tenants', sa.Column('plan', sa.VARCHAR(length=50), autoincrement=False, nullable=True))
    op.drop_column('tenants', 'plan_tier')
    op.drop_column('media_variants', 'provider')
    # ### end Alembic commands ###
