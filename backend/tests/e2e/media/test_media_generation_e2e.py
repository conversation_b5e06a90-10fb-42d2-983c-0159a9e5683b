"""
End-to-End tests for Media Generation APIs.

These tests verify the complete media generation workflow:
1. API request handling
2. Job creation in database
3. Queue task enqueuing
4. Worker task processing
5. Database updates
6. Response handling

Only the provider implementations are mocked - everything else is real E2E.
"""

import asyncio
import pytest
import time
import os
from pathlib import Path
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from modules.media.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.stores.models import Store


class TestMediaGenerationE2E:
    """End-to-end tests for media generation workflow."""

    @pytest.mark.asyncio
    async def test_generate_image_e2e_workflow(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test complete image generation workflow from API to completion."""
        
        # Prepare request with shop_id
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        # Step 1: Submit generation request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        
        assert response.status_code == 200
        response_data = response.json()
        assert "jobs" in response_data
        assert len(response_data["jobs"]) == 1
        
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        celery_task_id = job_info["celery_task_id"]
        
        cleanup_test_jobs(job_id)
        
        # Verify job was created in database
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        assert db_job is not None
        assert db_job.media_type == "image"
        assert db_job.status == MediaJobStatus.PENDING
        
        # Step 2: Wait for job completion by checking database directly
        import time
        max_wait = 30  # 30 seconds max wait
        start_time = time.time()

        while time.time() - start_time < max_wait:
            await db_session.refresh(db_job)
            if db_job.status == MediaJobStatus.COMPLETED:
                break
            await asyncio.sleep(2)

        # Verify job completed
        assert db_job.status == MediaJobStatus.COMPLETED
        assert db_job.completed_at is not None

        # Step 3: Verify database state after completion
        await db_session.refresh(db_job)
        assert db_job.status == MediaJobStatus.COMPLETED
        assert db_job.completed_at is not None

        # Verify variants were created and updated
        variants_result = await db_session.execute(
            select(MediaVariant).filter(MediaVariant.job_id == db_job.id)
        )
        variants = variants_result.scalars().all()
        assert len(variants) == 2

        for variant in variants:
            assert variant.status == MediaVariantStatus.READY
            assert variant.image_url is not None
            # Test provider returns data URLs or file paths
            assert variant.image_url.startswith("data:") or variant.image_url.endswith(".png")

    @pytest.mark.asyncio
    async def test_generate_video_e2e_workflow(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test complete video generation workflow from API to completion."""
        
        # Prepare request with shop_id
        request_data = sample_media_requests["video_request"].copy()
        request_data["shop_id"] = test_store.id
        
        # Step 1: Submit generation request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        
        assert response.status_code == 200
        response_data = response.json()
        
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        
        # Step 2: Wait for job completion
        completed_job = await wait_for_job_completion(e2e_client, db_job.id)
        
        assert completed_job["status"] == "completed"
        assert len(completed_job["variants"]) == 1  # Test provider returns 1 video

        # Step 3: Verify video-specific data
        for variant in completed_job["variants"]:
            assert variant["video_url"] is not None
            assert variant["thumbnail_url"] is not None
            assert variant["duration"] == 8.0  # Test provider returns 8 seconds
            assert variant["video_url"].endswith(".mp4")  # File path from test data

    @pytest.mark.asyncio
    async def test_generate_text_e2e_workflow(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test complete text generation workflow from API to completion."""
        
        # Prepare request with shop_id
        request_data = sample_media_requests["text_request"].copy()
        request_data["shop_id"] = test_store.id
        
        # Step 1: Submit generation request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        
        assert response.status_code == 200
        response_data = response.json()
        
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        
        # Step 2: Wait for job completion
        completed_job = await wait_for_job_completion(e2e_client, db_job.id)
        
        assert completed_job["status"] == "completed"
        
        # Step 3: Verify text content was generated
        # Text content might be stored differently, so check database directly
        await db_session.refresh(db_job)
        assert db_job.status == MediaJobStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_job_status_tracking_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test job status tracking throughout the workflow."""
        
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        # Submit job
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        db_job_id = db_job.id
        
        # Track status changes
        statuses_seen = []
        
        for _ in range(30):  # Check for up to 60 seconds
            status_response = await e2e_client.get(f"/api/media/jobs/{db_job_id}")
            if status_response.status_code == 200:
                status_data = status_response.json()
                current_status = status_data["status"]
                
                if current_status not in statuses_seen:
                    statuses_seen.append(current_status)
                
                if current_status in ["completed", "failed"]:
                    break
                    
            await asyncio.sleep(2)
        
        # Should see progression from pending to processing to completed
        assert "pending" in statuses_seen or "processing" in statuses_seen
        assert "completed" in statuses_seen or "failed" in statuses_seen

    @pytest.mark.asyncio
    async def test_multiple_products_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        cleanup_test_jobs
    ):
        """Test generation for multiple products in a single request."""
        
        request_data = {
            "product_title": "Multiple Products Test",
            "media_type": "image",
            "product_description": "Testing multiple products",
            "shop_id": test_store.id,
            "num_images": 2
        }
        
        # Submit request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200
        
        response_data = response.json()
        assert len(response_data["jobs"]) == 1  # Single job for the request

        # Register for cleanup
        job_info = response_data["jobs"][0]
        cleanup_test_jobs(job_info["job_id"])

        # Verify job was created in database
        job_id = job_info["job_id"]
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        assert db_job is not None
        assert db_job.media_type == "image"

    @pytest.mark.asyncio
    async def test_job_cancellation_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        cleanup_test_jobs
    ):
        """Test job cancellation workflow."""
        
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id
        
        # Submit job
        response = await e2e_client.post("/api/media/generate", json=request_data)
        job_info = response.json()["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)
        
        # Get database job ID
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()
        db_job_id = db_job.id
        
        # Cancel the job
        cancel_response = await e2e_client.post(f"/api/media/jobs/{db_job_id}/cancel")
        assert cancel_response.status_code == 200
        
        # Verify cancellation
        status_response = await e2e_client.get(f"/api/media/jobs/{db_job_id}")
        if status_response.status_code == 200:
            status_data = status_response.json()
            # Job might be cancelled or might have completed before cancellation
            assert status_data["status"] in ["cancelled", "completed", "failed"]

    @pytest.mark.asyncio
    async def test_file_storage_verification_e2e(
        self,
        e2e_client: AsyncClient,
        db_session: AsyncSession,
        test_store: Store,
        sample_media_requests,
        wait_for_job_completion,
        cleanup_test_jobs
    ):
        """Test that generated files are properly stored and accessible."""

        # Test image generation and file storage
        request_data = sample_media_requests["image_request"].copy()
        request_data["shop_id"] = test_store.id

        # Submit generation request
        response = await e2e_client.post("/api/media/generate", json=request_data)
        assert response.status_code == 200

        response_data = response.json()
        job_info = response_data["jobs"][0]
        job_id = job_info["job_id"]
        cleanup_test_jobs(job_id)

        # Get database job
        db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == job_id)
        )
        db_job = db_job_result.scalar_one_or_none()

        # Wait for completion
        completed_job = await wait_for_job_completion(e2e_client, db_job.id)
        assert completed_job["status"] == "completed"

        # Verify files exist in storage
        storage_dir = Path("backend/storage")
        assert storage_dir.exists()

        # Check for generated image files
        variants_result = await db_session.execute(
            select(MediaVariant).filter(MediaVariant.job_id == db_job.id)
        )
        variants = variants_result.scalars().all()

        for variant in variants:
            if variant.image_url and variant.image_url.startswith("data:"):
                # Base64 data URL - verify it contains valid data
                assert "base64," in variant.image_url
                assert len(variant.image_url.split("base64,")[1]) > 0
            elif variant.image_url and variant.image_url.endswith(".png"):
                # File path - verify file exists
                file_path = Path(variant.image_url)
                if file_path.exists():
                    assert file_path.stat().st_size > 0  # File has content

        # Test video generation and storage
        video_request = sample_media_requests["video_request"].copy()
        video_request["shop_id"] = test_store.id

        video_response = await e2e_client.post("/api/media/generate", json=video_request)
        assert video_response.status_code == 200

        video_response_data = video_response.json()
        video_job_info = video_response_data["jobs"][0]
        video_job_id = video_job_info["job_id"]
        cleanup_test_jobs(video_job_id)

        # Get video database job
        video_db_job_result = await db_session.execute(
            select(MediaJob).filter(MediaJob.job_id == video_job_id)
        )
        video_db_job = video_db_job_result.scalar_one_or_none()

        # Wait for video completion
        video_completed_job = await wait_for_job_completion(e2e_client, video_db_job.id)
        assert video_completed_job["status"] == "completed"

        # Verify video files
        video_variants_result = await db_session.execute(
            select(MediaVariant).filter(MediaVariant.job_id == video_db_job.id)
        )
        video_variants = video_variants_result.scalars().all()

        for variant in video_variants:
            if variant.video_url and variant.video_url.endswith(".mp4"):
                file_path = Path(variant.video_url)
                if file_path.exists():
                    assert file_path.stat().st_size > 0  # Video file has content
                    assert variant.duration == 8.0  # Test provider duration
