"""initial migration

Revision ID: 41cae0401133
Revises: 
Create Date: 2025-09-07 12:12:49.283603

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '41cae0401133'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('email_verifications',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_used', sa.<PERSON>(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_email_verifications_email'), 'email_verifications', ['email'], unique=False)
    op.create_index(op.f('ix_email_verifications_id'), 'email_verifications', ['id'], unique=False)
    op.create_index(op.f('ix_email_verifications_token'), 'email_verifications', ['token'], unique=True)
    op.create_table('holidays',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('holiday_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('holiday_date')
    )
    op.create_index(op.f('ix_holidays_id'), 'holidays', ['id'], unique=False)
    op.create_table('templates',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('provider', sa.String(), nullable=False),
    sa.Column('template_config', sa.JSON(), nullable=False),
    sa.Column('preview_url', sa.String(), nullable=True),
    sa.Column('category', sa.String(), nullable=True),
    sa.Column('tags', sa.JSON(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('plan_tier_required', sa.Enum('FREE', 'STARTER', 'GROWTH', 'PRO', 'ENTERPRISE', name='plan_tier'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_templates_id'), 'templates', ['id'], unique=False)
    op.create_table('users',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=True),
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('username', sa.String(length=100), nullable=True),
    sa.Column('first_name', sa.String(length=100), nullable=True),
    sa.Column('last_name', sa.String(length=100), nullable=True),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('password_hash', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('role', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_login_at', sa.DateTime(), nullable=True),
    sa.Column('email_verification_token', sa.String(length=255), nullable=True),
    sa.Column('email_verification_expires', sa.DateTime(), nullable=True),
    sa.Column('password_reset_token', sa.String(length=255), nullable=True),
    sa.Column('password_reset_expires', sa.DateTime(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_uuid'), 'users', ['uuid'], unique=True)
    op.create_table('voices',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('provider', sa.String(), nullable=False),
    sa.Column('provider_voice_id', sa.String(), nullable=False),
    sa.Column('gender', sa.String(), nullable=True),
    sa.Column('accent', sa.String(), nullable=True),
    sa.Column('language', sa.String(), nullable=False),
    sa.Column('sample_url', sa.String(), nullable=True),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('plan_tier_required', sa.Enum('FREE', 'STARTER', 'GROWTH', 'PRO', 'ENTERPRISE', name='plan_tier'), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_voices_id'), 'voices', ['id'], unique=False)
    op.create_table('oauth_accounts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('provider', sa.String(length=50), nullable=False),
    sa.Column('provider_user_id', sa.String(length=255), nullable=False),
    sa.Column('provider_username', sa.String(length=255), nullable=True),
    sa.Column('provider_email', sa.String(length=255), nullable=True),
    sa.Column('access_token', sa.Text(), nullable=True),
    sa.Column('refresh_token', sa.Text(), nullable=True),
    sa.Column('token_expires_at', sa.DateTime(), nullable=True),
    sa.Column('provider_data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('provider', 'provider_user_id', name='unique_provider_user')
    )
    op.create_index(op.f('ix_oauth_accounts_id'), 'oauth_accounts', ['id'], unique=False)
    op.create_table('password_resets',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('is_used', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_password_resets_id'), 'password_resets', ['id'], unique=False)
    op.create_index(op.f('ix_password_resets_token'), 'password_resets', ['token'], unique=True)
    op.create_table('tenants',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('uuid', sa.UUID(), nullable=True),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('slug', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('plan', sa.String(length=50), nullable=True),
    sa.Column('storage_limit_gb', sa.Float(), nullable=True),
    sa.Column('storage_used_gb', sa.Float(), nullable=True),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=True),
    sa.Column('trial_ends_at', sa.DateTime(), nullable=True),
    sa.Column('shop_domain', sa.String(length=255), nullable=True),
    sa.Column('shop_id', sa.String(length=50), nullable=True),
    sa.Column('billing_email', sa.String(length=255), nullable=True),
    sa.Column('settings', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('updated_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tenants_id'), 'tenants', ['id'], unique=False)
    op.create_index(op.f('ix_tenants_slug'), 'tenants', ['slug'], unique=True)
    op.create_index(op.f('ix_tenants_uuid'), 'tenants', ['uuid'], unique=True)
    op.create_table('user_sessions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('session_token', sa.String(length=255), nullable=False),
    sa.Column('refresh_token', sa.String(length=255), nullable=False),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('device_name', sa.String(length=255), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=True),
    sa.Column('last_used_at', sa.DateTime(), nullable=True),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_user_sessions_id'), 'user_sessions', ['id'], unique=False)
    op.create_index(op.f('ix_user_sessions_refresh_token'), 'user_sessions', ['refresh_token'], unique=True)
    op.create_index(op.f('ix_user_sessions_session_token'), 'user_sessions', ['session_token'], unique=True)
    op.create_table('billing_usage',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('usage_type', sa.Enum('VIDEO_GENERATION', 'STORAGE_GB', 'BANDWIDTH_GB', 'API_CALLS', name='usagetype'), nullable=False),
    sa.Column('quantity', sa.Float(), nullable=False),
    sa.Column('unit_price', sa.DECIMAL(precision=10, scale=4), nullable=True),
    sa.Column('total_cost', sa.DECIMAL(precision=10, scale=2), nullable=True),
    sa.Column('billing_period_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('billing_period_end', sa.DateTime(timezone=True), nullable=False),
    sa.Column('stripe_usage_record_id', sa.String(length=255), nullable=True),
    sa.Column('stripe_subscription_item_id', sa.String(length=255), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('usage_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_billing_usage_id'), 'billing_usage', ['id'], unique=False)
    op.create_index(op.f('ix_billing_usage_stripe_usage_record_id'), 'billing_usage', ['stripe_usage_record_id'], unique=True)
    op.create_table('invoices',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('stripe_invoice_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=False),
    sa.Column('amount_due', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('amount_paid', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('status', sa.String(length=50), nullable=False),
    sa.Column('invoice_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('due_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('paid_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('period_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('period_end', sa.DateTime(timezone=True), nullable=False),
    sa.Column('invoice_pdf_url', sa.String(length=500), nullable=True),
    sa.Column('hosted_invoice_url', sa.String(length=500), nullable=True),
    sa.Column('invoice_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_invoices_id'), 'invoices', ['id'], unique=False)
    op.create_index(op.f('ix_invoices_stripe_invoice_id'), 'invoices', ['stripe_invoice_id'], unique=True)
    op.create_table('media_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', name='mediajobstatus'), nullable=False),
    sa.Column('media_type', sa.String(), nullable=False),
    sa.Column('provider', sa.String(), nullable=False),
    sa.Column('template_id', sa.String(), nullable=True),
    sa.Column('voice_id', sa.String(), nullable=True),
    sa.Column('script', sa.Text(), nullable=True),
    sa.Column('custom_config', sa.JSON(), nullable=True),
    sa.Column('progress_percentage', sa.Float(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_jobs_id'), 'media_jobs', ['id'], unique=False)
    op.create_table('payment_methods',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('stripe_payment_method_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=False),
    sa.Column('type', sa.String(length=50), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('card_brand', sa.String(length=50), nullable=True),
    sa.Column('card_last4', sa.String(length=4), nullable=True),
    sa.Column('card_exp_month', sa.Integer(), nullable=True),
    sa.Column('card_exp_year', sa.Integer(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('payment_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_payment_methods_id'), 'payment_methods', ['id'], unique=False)
    op.create_index(op.f('ix_payment_methods_stripe_payment_method_id'), 'payment_methods', ['stripe_payment_method_id'], unique=True)
    op.create_table('stores',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(), nullable=False),
    sa.Column('platform', sa.String(), nullable=False),
    sa.Column('api_key', sa.String(), nullable=True),
    sa.Column('api_secret_key', sa.String(), nullable=True),
    sa.Column('admin_access_token', sa.String(), nullable=True),
    sa.Column('storefront_access_token', sa.String(), nullable=True),
    sa.Column('shop_domain', sa.String(), nullable=True),
    sa.Column('shop_id', sa.String(), nullable=True),
    sa.Column('shop_name', sa.String(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('last_sync', sa.DateTime(timezone=True), nullable=True),
    sa.Column('product_sync_interval', sa.String(), nullable=True),
    sa.Column('inventory_sync_interval', sa.String(), nullable=True),
    sa.Column('customer_sync_interval', sa.String(), nullable=True),
    sa.Column('airbyte_source_id', sa.String(length=255), nullable=True),
    sa.Column('airbyte_destination_id', sa.String(length=255), nullable=True),
    sa.Column('airbyte_connection_id', sa.String(length=255), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('owner_id', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.Integer(), nullable=True),
    sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_stores_airbyte_connection_id'), 'stores', ['airbyte_connection_id'], unique=False)
    op.create_index(op.f('ix_stores_airbyte_source_id'), 'stores', ['airbyte_source_id'], unique=False)
    op.create_index(op.f('ix_stores_id'), 'stores', ['id'], unique=False)
    op.create_table('subscriptions',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('stripe_subscription_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_customer_id', sa.String(length=255), nullable=False),
    sa.Column('stripe_price_id', sa.String(length=255), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'PAST_DUE', 'CANCELED', 'UNPAID', 'INCOMPLETE', 'INCOMPLETE_EXPIRED', 'TRIALING', name='subscriptionstatus'), nullable=False),
    sa.Column('current_period_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('current_period_end', sa.DateTime(timezone=True), nullable=False),
    sa.Column('trial_start', sa.DateTime(timezone=True), nullable=True),
    sa.Column('trial_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('subscription_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_subscriptions_id'), 'subscriptions', ['id'], unique=False)
    op.create_index(op.f('ix_subscriptions_stripe_subscription_id'), 'subscriptions', ['stripe_subscription_id'], unique=True)
    op.create_table('customers',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('email', sa.String(), nullable=False),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('accepts_marketing', sa.Boolean(), nullable=True),
    sa.Column('tax_exempt', sa.Boolean(), nullable=True),
    sa.Column('verified_email', sa.Boolean(), nullable=True),
    sa.Column('address1', sa.String(), nullable=True),
    sa.Column('address2', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('province', sa.String(), nullable=True),
    sa.Column('province_code', sa.String(), nullable=True),
    sa.Column('zip', sa.String(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('tags', sa.String(), nullable=True),
    sa.Column('note', sa.Text(), nullable=True),
    sa.Column('total_spent', sa.Float(), nullable=True),
    sa.Column('orders_count', sa.Integer(), nullable=True),
    sa.Column('state', sa.String(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('email'),
    sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_customers_id'), 'customers', ['id'], unique=False)
    op.create_table('dead_letter_queue',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('source_type', sa.String(length=50), nullable=False),
    sa.Column('source_id', sa.String(length=255), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('original_payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('failure_reason', sa.String(length=100), nullable=False),
    sa.Column('error_message', sa.Text(), nullable=False),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('last_retry_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('resolved', sa.Boolean(), nullable=True),
    sa.Column('resolved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_dlq_created_at', 'dead_letter_queue', ['created_at'], unique=False)
    op.create_index('idx_dlq_store_id', 'dead_letter_queue', ['store_id'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_id'), 'dead_letter_queue', ['id'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_resolved'), 'dead_letter_queue', ['resolved'], unique=False)
    op.create_index(op.f('ix_dead_letter_queue_source_type'), 'dead_letter_queue', ['source_type'], unique=False)
    op.create_table('forecasts',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('forecast_date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('predicted_sales', sa.Float(), nullable=False),
    sa.Column('confidence_lower', sa.Float(), nullable=True),
    sa.Column('confidence_upper', sa.Float(), nullable=True),
    sa.Column('forecast_period', sa.String(), nullable=True),
    sa.Column('raw_forecast_data', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_forecasts_id'), 'forecasts', ['id'], unique=False)
    op.create_table('media_variants',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('job_id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('external_variant_id', sa.String(), nullable=True),
    sa.Column('variant_name', sa.String(), nullable=False),
    sa.Column('status', sa.Enum('GENERATING', 'READY', 'FAILED', 'PROCESSING', name='mediavariantstatus'), nullable=False),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('resolution', sa.String(), nullable=True),
    sa.Column('file_size_bytes', sa.Integer(), nullable=True),
    sa.Column('video_url', sa.String(), nullable=True),
    sa.Column('image_url', sa.String(), nullable=True),
    sa.Column('voice_url', sa.String(), nullable=True),
    sa.Column('thumbnail_url', sa.String(), nullable=True),
    sa.Column('provider_media_id', sa.String(), nullable=True),
    sa.Column('provider_metadata', sa.JSON(), nullable=True),
    sa.Column('is_favorite', sa.Boolean(), nullable=True),
    sa.Column('user_rating', sa.Integer(), nullable=True),
    sa.Column('push_status', sa.Enum('PENDING', 'PUSHING', 'COMPLETED', 'FAILED', name='pushstatus'), nullable=False),
    sa.Column('media_id', sa.String(), nullable=True),
    sa.Column('pushed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('push_error_message', sa.Text(), nullable=True),
    sa.Column('alt_text', sa.String(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['media_jobs.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_media_variants_id'), 'media_variants', ['id'], unique=False)
    op.create_table('product_performance',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('total_sold', sa.Integer(), nullable=True),
    sa.Column('total_revenue', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('product_id')
    )
    op.create_index(op.f('ix_product_performance_id'), 'product_performance', ['id'], unique=False)
    op.create_table('products',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('title', sa.String(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('compare_at_price', sa.Float(), nullable=True),
    sa.Column('cost', sa.Float(), nullable=True),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('barcode', sa.String(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('weight', sa.Float(), nullable=True),
    sa.Column('weight_unit', sa.String(), nullable=True),
    sa.Column('vendor', sa.String(), nullable=True),
    sa.Column('product_type', sa.String(), nullable=True),
    sa.Column('tags', sa.String(), nullable=True),
    sa.Column('status', sa.String(), nullable=True),
    sa.Column('published', sa.Boolean(), nullable=True),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('handle', sa.String(), nullable=True),
    sa.Column('images', sa.Text(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('platform_data', sa.Text(), nullable=True),
    sa.Column('published_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('featured_image', sa.Text(), nullable=True),
    sa.Column('options', sa.Text(), nullable=True),
    sa.Column('seo', sa.Text(), nullable=True),
    sa.Column('metafields', sa.Text(), nullable=True),
    sa.Column('collections', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_products_id'), 'products', ['id'], unique=False)
    op.create_table('store_analytics_snapshots',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('date', sa.Date(), nullable=False),
    sa.Column('total_sales', sa.Float(), nullable=True),
    sa.Column('total_orders', sa.Integer(), nullable=True),
    sa.Column('average_order_value', sa.Float(), nullable=True),
    sa.Column('new_customers', sa.Integer(), nullable=True),
    sa.Column('conversion_rate', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('date')
    )
    op.create_index(op.f('ix_store_analytics_snapshots_id'), 'store_analytics_snapshots', ['id'], unique=False)
    op.create_table('store_sales',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.String(), nullable=True),
    sa.Column('variant_id', sa.String(), nullable=True),
    sa.Column('sale_date', sa.Date(), nullable=False),
    sa.Column('quantity_sold', sa.Integer(), nullable=False),
    sa.Column('revenue', sa.Float(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_store_sales_id'), 'store_sales', ['id'], unique=False)
    op.create_table('sync_checkpoints',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('last_updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_id', sa.String(length=255), nullable=True),
    sa.Column('airbyte_state', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('total_records', sa.Integer(), nullable=True),
    sa.Column('last_sync_status', sa.String(length=20), nullable=True),
    sa.Column('last_error_message', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_successful_sync_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('store_id', 'entity_type', name='uq_sync_checkpoints_store_entity')
    )
    op.create_index('idx_sync_checkpoints_store_entity', 'sync_checkpoints', ['store_id', 'entity_type'], unique=False)
    op.create_index('idx_sync_checkpoints_updated_at', 'sync_checkpoints', ['last_updated_at'], unique=False)
    op.create_index(op.f('ix_sync_checkpoints_id'), 'sync_checkpoints', ['id'], unique=False)
    op.create_table('sync_jobs',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('entity_type', sa.String(length=50), nullable=False),
    sa.Column('job_type', sa.String(length=20), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('triggered_by', sa.String(length=50), nullable=True),
    sa.Column('airbyte_job_id', sa.BigInteger(), nullable=True),
    sa.Column('airbyte_connection_id', sa.String(length=255), nullable=True),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('finished_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('records_processed', sa.Integer(), nullable=True),
    sa.Column('records_failed', sa.Integer(), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.Column('error_details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('celery_task_id', sa.String(length=255), nullable=True),
    sa.Column('job_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_sync_jobs_created_at', 'sync_jobs', ['created_at'], unique=False)
    op.create_index('idx_sync_jobs_store_entity', 'sync_jobs', ['store_id', 'entity_type'], unique=False)
    op.create_index(op.f('ix_sync_jobs_airbyte_job_id'), 'sync_jobs', ['airbyte_job_id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_celery_task_id'), 'sync_jobs', ['celery_task_id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_id'), 'sync_jobs', ['id'], unique=False)
    op.create_index(op.f('ix_sync_jobs_status'), 'sync_jobs', ['status'], unique=False)
    op.create_table('sync_progress',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('sync_type', sa.String(), nullable=False),
    sa.Column('status', sa.String(), nullable=False),
    sa.Column('total_items', sa.Integer(), nullable=True),
    sa.Column('processed_items', sa.Integer(), nullable=True),
    sa.Column('current_batch', sa.Integer(), nullable=True),
    sa.Column('total_batches', sa.Integer(), nullable=True),
    sa.Column('last_update', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('error_message', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_sync_progress_id'), 'sync_progress', ['id'], unique=False)
    op.create_table('webhook_events',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('event_id', sa.String(length=255), nullable=False),
    sa.Column('topic', sa.String(length=100), nullable=False),
    sa.Column('shop_domain', sa.String(length=255), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=True),
    sa.Column('tenant_id', sa.Integer(), nullable=True),
    sa.Column('event_type', sa.String(length=50), nullable=True),
    sa.Column('payload', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('headers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('hmac_verified', sa.Boolean(), nullable=True),
    sa.Column('status', sa.String(length=20), nullable=True),
    sa.Column('retry_count', sa.Integer(), nullable=True),
    sa.Column('max_retries', sa.Integer(), nullable=True),
    sa.Column('last_error', sa.Text(), nullable=True),
    sa.Column('processing_started_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_webhook_events_created_at', 'webhook_events', ['created_at'], unique=False)
    op.create_index('idx_webhook_events_shop_topic', 'webhook_events', ['shop_domain', 'topic'], unique=False)
    op.create_index(op.f('ix_webhook_events_event_id'), 'webhook_events', ['event_id'], unique=True)
    op.create_index(op.f('ix_webhook_events_id'), 'webhook_events', ['id'], unique=False)
    op.create_index(op.f('ix_webhook_events_shop_domain'), 'webhook_events', ['shop_domain'], unique=False)
    op.create_index(op.f('ix_webhook_events_status'), 'webhook_events', ['status'], unique=False)
    op.create_index(op.f('ix_webhook_events_topic'), 'webhook_events', ['topic'], unique=False)
    op.create_table('ab_test_experiments',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('product_ids', sa.JSON(), nullable=False),
    sa.Column('control_variant_id', sa.Integer(), nullable=False),
    sa.Column('test_variant_ids', sa.JSON(), nullable=False),
    sa.Column('traffic_allocation', sa.Float(), nullable=False),
    sa.Column('control_split', sa.Float(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('start_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('end_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('statistical_significance', sa.Float(), nullable=True),
    sa.Column('confidence_level', sa.Float(), nullable=False),
    sa.Column('winner_variant_id', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['control_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['winner_variant_id'], ['media_variants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_ab_test_experiments_id'), 'ab_test_experiments', ['id'], unique=False)
    op.create_table('analytics_events',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('event_id', sa.String(length=255), nullable=False),
    sa.Column('event_type', sa.String(length=50), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=True),
    sa.Column('user_id', sa.String(length=255), nullable=True),
    sa.Column('media_variant_id', sa.Integer(), nullable=True),
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('timestamp', sa.DateTime(timezone=True), nullable=False),
    sa.Column('duration', sa.Float(), nullable=True),
    sa.Column('position', sa.Float(), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('country', sa.String(length=2), nullable=True),
    sa.Column('device_type', sa.String(length=50), nullable=True),
    sa.Column('referrer', sa.Text(), nullable=True),
    sa.Column('utm_source', sa.String(length=255), nullable=True),
    sa.Column('utm_medium', sa.String(length=255), nullable=True),
    sa.Column('utm_campaign', sa.String(length=255), nullable=True),
    sa.Column('utm_content', sa.String(length=255), nullable=True),
    sa.Column('utm_term', sa.String(length=255), nullable=True),
    sa.Column('order_id', sa.String(length=255), nullable=True),
    sa.Column('order_value', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('properties', sa.JSON(), nullable=True),
    sa.Column('is_processed', sa.Boolean(), nullable=False),
    sa.Column('is_conversion', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('processed_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_analytics_events_conversion', 'analytics_events', ['is_conversion', 'timestamp'], unique=False)
    op.create_index('idx_analytics_events_product_timestamp', 'analytics_events', ['product_id', 'timestamp'], unique=False)
    op.create_index('idx_analytics_events_session_timestamp', 'analytics_events', ['session_id', 'timestamp'], unique=False)
    op.create_index('idx_analytics_events_tenant_timestamp', 'analytics_events', ['tenant_id', 'timestamp'], unique=False)
    op.create_index(op.f('ix_analytics_events_event_id'), 'analytics_events', ['event_id'], unique=True)
    op.create_index(op.f('ix_analytics_events_id'), 'analytics_events', ['id'], unique=False)
    op.create_index(op.f('ix_analytics_events_order_id'), 'analytics_events', ['order_id'], unique=False)
    op.create_index(op.f('ix_analytics_events_product_id'), 'analytics_events', ['product_id'], unique=False)
    op.create_index(op.f('ix_analytics_events_session_id'), 'analytics_events', ['session_id'], unique=False)
    op.create_index(op.f('ix_analytics_events_timestamp'), 'analytics_events', ['timestamp'], unique=False)
    op.create_index(op.f('ix_analytics_events_user_id'), 'analytics_events', ['user_id'], unique=False)
    op.create_table('customer_addresses',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('external_id', sa.String(), nullable=True),
    sa.Column('first_name', sa.String(), nullable=True),
    sa.Column('last_name', sa.String(), nullable=True),
    sa.Column('company', sa.String(), nullable=True),
    sa.Column('address1', sa.String(), nullable=True),
    sa.Column('address2', sa.String(), nullable=True),
    sa.Column('city', sa.String(), nullable=True),
    sa.Column('province', sa.String(), nullable=True),
    sa.Column('province_code', sa.String(), nullable=True),
    sa.Column('zip', sa.String(), nullable=True),
    sa.Column('country', sa.String(), nullable=True),
    sa.Column('country_code', sa.String(), nullable=True),
    sa.Column('phone', sa.String(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_customer_addresses_id'), 'customer_addresses', ['id'], unique=False)
    op.create_table('media_analytics',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('media_variant_id', sa.Integer(), nullable=False),
    sa.Column('product_id', sa.String(length=255), nullable=False),
    sa.Column('date', sa.DateTime(timezone=True), nullable=False),
    sa.Column('views', sa.Integer(), nullable=False),
    sa.Column('unique_views', sa.Integer(), nullable=False),
    sa.Column('plays', sa.Integer(), nullable=False),
    sa.Column('completions', sa.Integer(), nullable=False),
    sa.Column('total_watch_time', sa.Float(), nullable=False),
    sa.Column('average_watch_time', sa.Float(), nullable=False),
    sa.Column('completion_rate', sa.Float(), nullable=False),
    sa.Column('cta_clicks', sa.Integer(), nullable=False),
    sa.Column('cta_click_rate', sa.Float(), nullable=False),
    sa.Column('add_to_carts', sa.Integer(), nullable=False),
    sa.Column('purchases', sa.Integer(), nullable=False),
    sa.Column('conversion_rate', sa.Float(), nullable=False),
    sa.Column('total_revenue', sa.Float(), nullable=False),
    sa.Column('mobile_views', sa.Integer(), nullable=False),
    sa.Column('desktop_views', sa.Integer(), nullable=False),
    sa.Column('tablet_views', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_media_analytics_product_date', 'media_analytics', ['product_id', 'date'], unique=False)
    op.create_index('idx_media_analytics_tenant_date', 'media_analytics', ['tenant_id', 'date'], unique=False)
    op.create_index('idx_media_analytics_variant_date', 'media_analytics', ['media_variant_id', 'date'], unique=False)
    op.create_index(op.f('ix_media_analytics_date'), 'media_analytics', ['date'], unique=False)
    op.create_index(op.f('ix_media_analytics_id'), 'media_analytics', ['id'], unique=False)
    op.create_index(op.f('ix_media_analytics_product_id'), 'media_analytics', ['product_id'], unique=False)
    op.create_table('orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('store_id', sa.Integer(), nullable=False),
    sa.Column('customer_id', sa.Integer(), nullable=True),
    sa.Column('order_number', sa.String(), nullable=True),
    sa.Column('name', sa.String(), nullable=True),
    sa.Column('email', sa.String(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=True),
    sa.Column('subtotal_price', sa.Float(), nullable=True),
    sa.Column('total_tax', sa.Float(), nullable=True),
    sa.Column('total_discounts', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(), nullable=True),
    sa.Column('financial_status', sa.String(), nullable=True),
    sa.Column('fulfillment_status', sa.String(), nullable=True),
    sa.Column('full_json', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['customer_id'], ['customers.id'], ),
    sa.ForeignKeyConstraint(['store_id'], ['stores.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_orders_id'), 'orders', ['id'], unique=False)
    op.create_table('product_variants',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=False),
    sa.Column('product_id', sa.Integer(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('price', sa.Float(), nullable=False),
    sa.Column('compare_at_price', sa.Float(), nullable=True),
    sa.Column('cost', sa.Float(), nullable=True),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('barcode', sa.String(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('weight', sa.Float(), nullable=True),
    sa.Column('weight_unit', sa.String(), nullable=True),
    sa.Column('option1', sa.String(), nullable=True),
    sa.Column('option2', sa.String(), nullable=True),
    sa.Column('option3', sa.String(), nullable=True),
    sa.Column('taxable', sa.Boolean(), nullable=True),
    sa.Column('requires_shipping', sa.Boolean(), nullable=True),
    sa.Column('inventory_policy', sa.String(), nullable=True),
    sa.Column('fulfillment_service', sa.Text(), nullable=True),
    sa.Column('inventory_item', sa.Text(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['product_id'], ['products.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('external_id')
    )
    op.create_index(op.f('ix_product_variants_id'), 'product_variants', ['id'], unique=False)
    op.create_table('conversion_funnels',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('session_id', sa.String(length=255), nullable=False),
    sa.Column('user_id', sa.String(length=255), nullable=True),
    sa.Column('media_view_event_id', sa.String(length=255), nullable=True),
    sa.Column('media_play_event_id', sa.String(length=255), nullable=True),
    sa.Column('media_complete_event_id', sa.String(length=255), nullable=True),
    sa.Column('cta_click_event_id', sa.String(length=255), nullable=True),
    sa.Column('add_to_cart_event_id', sa.String(length=255), nullable=True),
    sa.Column('purchase_event_id', sa.String(length=255), nullable=True),
    sa.Column('first_media_variant_id', sa.Integer(), nullable=True),
    sa.Column('converting_media_variant_id', sa.Integer(), nullable=True),
    sa.Column('product_id', sa.String(length=255), nullable=True),
    sa.Column('time_to_conversion', sa.Integer(), nullable=True),
    sa.Column('conversion_value', sa.Float(), nullable=True),
    sa.Column('currency', sa.String(length=3), nullable=True),
    sa.Column('funnel_start', sa.DateTime(timezone=True), nullable=False),
    sa.Column('funnel_end', sa.DateTime(timezone=True), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['add_to_cart_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['converting_media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['cta_click_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['first_media_variant_id'], ['media_variants.id'], ),
    sa.ForeignKeyConstraint(['media_complete_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['media_play_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['media_view_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['purchase_event_id'], ['analytics_events.event_id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_conversion_funnels_id'), 'conversion_funnels', ['id'], unique=False)
    op.create_index(op.f('ix_conversion_funnels_product_id'), 'conversion_funnels', ['product_id'], unique=False)
    op.create_index(op.f('ix_conversion_funnels_session_id'), 'conversion_funnels', ['session_id'], unique=False)
    op.create_index(op.f('ix_conversion_funnels_user_id'), 'conversion_funnels', ['user_id'], unique=False)
    op.create_table('order_line_items',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('order_id', sa.Integer(), nullable=False),
    sa.Column('external_id', sa.String(), nullable=True),
    sa.Column('product_id', sa.String(), nullable=True),
    sa.Column('variant_id', sa.String(), nullable=True),
    sa.Column('title', sa.String(), nullable=True),
    sa.Column('sku', sa.String(), nullable=True),
    sa.Column('quantity', sa.Integer(), nullable=True),
    sa.Column('price', sa.Float(), nullable=True),
    sa.Column('total_price', sa.Float(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['order_id'], ['orders.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_order_line_items_id'), 'order_line_items', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_order_line_items_id'), table_name='order_line_items')
    op.drop_table('order_line_items')
    op.drop_index(op.f('ix_conversion_funnels_user_id'), table_name='conversion_funnels')
    op.drop_index(op.f('ix_conversion_funnels_session_id'), table_name='conversion_funnels')
    op.drop_index(op.f('ix_conversion_funnels_product_id'), table_name='conversion_funnels')
    op.drop_index(op.f('ix_conversion_funnels_id'), table_name='conversion_funnels')
    op.drop_table('conversion_funnels')
    op.drop_index(op.f('ix_product_variants_id'), table_name='product_variants')
    op.drop_table('product_variants')
    op.drop_index(op.f('ix_orders_id'), table_name='orders')
    op.drop_table('orders')
    op.drop_index(op.f('ix_media_analytics_product_id'), table_name='media_analytics')
    op.drop_index(op.f('ix_media_analytics_id'), table_name='media_analytics')
    op.drop_index(op.f('ix_media_analytics_date'), table_name='media_analytics')
    op.drop_index('idx_media_analytics_variant_date', table_name='media_analytics')
    op.drop_index('idx_media_analytics_tenant_date', table_name='media_analytics')
    op.drop_index('idx_media_analytics_product_date', table_name='media_analytics')
    op.drop_table('media_analytics')
    op.drop_index(op.f('ix_customer_addresses_id'), table_name='customer_addresses')
    op.drop_table('customer_addresses')
    op.drop_index(op.f('ix_analytics_events_user_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_timestamp'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_session_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_product_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_order_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_id'), table_name='analytics_events')
    op.drop_index(op.f('ix_analytics_events_event_id'), table_name='analytics_events')
    op.drop_index('idx_analytics_events_tenant_timestamp', table_name='analytics_events')
    op.drop_index('idx_analytics_events_session_timestamp', table_name='analytics_events')
    op.drop_index('idx_analytics_events_product_timestamp', table_name='analytics_events')
    op.drop_index('idx_analytics_events_conversion', table_name='analytics_events')
    op.drop_table('analytics_events')
    op.drop_index(op.f('ix_ab_test_experiments_id'), table_name='ab_test_experiments')
    op.drop_table('ab_test_experiments')
    op.drop_index(op.f('ix_webhook_events_topic'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_status'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_shop_domain'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_id'), table_name='webhook_events')
    op.drop_index(op.f('ix_webhook_events_event_id'), table_name='webhook_events')
    op.drop_index('idx_webhook_events_shop_topic', table_name='webhook_events')
    op.drop_index('idx_webhook_events_created_at', table_name='webhook_events')
    op.drop_table('webhook_events')
    op.drop_index(op.f('ix_sync_progress_id'), table_name='sync_progress')
    op.drop_table('sync_progress')
    op.drop_index(op.f('ix_sync_jobs_status'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_celery_task_id'), table_name='sync_jobs')
    op.drop_index(op.f('ix_sync_jobs_airbyte_job_id'), table_name='sync_jobs')
    op.drop_index('idx_sync_jobs_store_entity', table_name='sync_jobs')
    op.drop_index('idx_sync_jobs_created_at', table_name='sync_jobs')
    op.drop_table('sync_jobs')
    op.drop_index(op.f('ix_sync_checkpoints_id'), table_name='sync_checkpoints')
    op.drop_index('idx_sync_checkpoints_updated_at', table_name='sync_checkpoints')
    op.drop_index('idx_sync_checkpoints_store_entity', table_name='sync_checkpoints')
    op.drop_table('sync_checkpoints')
    op.drop_index(op.f('ix_store_sales_id'), table_name='store_sales')
    op.drop_table('store_sales')
    op.drop_index(op.f('ix_store_analytics_snapshots_id'), table_name='store_analytics_snapshots')
    op.drop_table('store_analytics_snapshots')
    op.drop_index(op.f('ix_products_id'), table_name='products')
    op.drop_table('products')
    op.drop_index(op.f('ix_product_performance_id'), table_name='product_performance')
    op.drop_table('product_performance')
    op.drop_index(op.f('ix_media_variants_id'), table_name='media_variants')
    op.drop_table('media_variants')
    op.drop_index(op.f('ix_forecasts_id'), table_name='forecasts')
    op.drop_table('forecasts')
    op.drop_index(op.f('ix_dead_letter_queue_source_type'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_resolved'), table_name='dead_letter_queue')
    op.drop_index(op.f('ix_dead_letter_queue_id'), table_name='dead_letter_queue')
    op.drop_index('idx_dlq_store_id', table_name='dead_letter_queue')
    op.drop_index('idx_dlq_created_at', table_name='dead_letter_queue')
    op.drop_table('dead_letter_queue')
    op.drop_index(op.f('ix_customers_id'), table_name='customers')
    op.drop_table('customers')
    op.drop_index(op.f('ix_subscriptions_stripe_subscription_id'), table_name='subscriptions')
    op.drop_index(op.f('ix_subscriptions_id'), table_name='subscriptions')
    op.drop_table('subscriptions')
    op.drop_index(op.f('ix_stores_id'), table_name='stores')
    op.drop_index(op.f('ix_stores_airbyte_source_id'), table_name='stores')
    op.drop_index(op.f('ix_stores_airbyte_connection_id'), table_name='stores')
    op.drop_table('stores')
    op.drop_index(op.f('ix_payment_methods_stripe_payment_method_id'), table_name='payment_methods')
    op.drop_index(op.f('ix_payment_methods_id'), table_name='payment_methods')
    op.drop_table('payment_methods')
    op.drop_index(op.f('ix_media_jobs_id'), table_name='media_jobs')
    op.drop_table('media_jobs')
    op.drop_index(op.f('ix_invoices_stripe_invoice_id'), table_name='invoices')
    op.drop_index(op.f('ix_invoices_id'), table_name='invoices')
    op.drop_table('invoices')
    op.drop_index(op.f('ix_billing_usage_stripe_usage_record_id'), table_name='billing_usage')
    op.drop_index(op.f('ix_billing_usage_id'), table_name='billing_usage')
    op.drop_table('billing_usage')
    op.drop_index(op.f('ix_user_sessions_session_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_refresh_token'), table_name='user_sessions')
    op.drop_index(op.f('ix_user_sessions_id'), table_name='user_sessions')
    op.drop_table('user_sessions')
    op.drop_index(op.f('ix_tenants_uuid'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_slug'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_id'), table_name='tenants')
    op.drop_table('tenants')
    op.drop_index(op.f('ix_password_resets_token'), table_name='password_resets')
    op.drop_index(op.f('ix_password_resets_id'), table_name='password_resets')
    op.drop_table('password_resets')
    op.drop_index(op.f('ix_oauth_accounts_id'), table_name='oauth_accounts')
    op.drop_table('oauth_accounts')
    op.drop_index(op.f('ix_voices_id'), table_name='voices')
    op.drop_table('voices')
    op.drop_index(op.f('ix_users_uuid'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_templates_id'), table_name='templates')
    op.drop_table('templates')
    op.drop_index(op.f('ix_holidays_id'), table_name='holidays')
    op.drop_table('holidays')
    op.drop_index(op.f('ix_email_verifications_token'), table_name='email_verifications')
    op.drop_index(op.f('ix_email_verifications_id'), table_name='email_verifications')
    op.drop_index(op.f('ix_email_verifications_email'), table_name='email_verifications')
    op.drop_table('email_verifications')
    # ### end Alembic commands ###
