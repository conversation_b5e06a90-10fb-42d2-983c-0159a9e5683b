#!/usr/bin/env python3
"""
Test script to verify that extra fields are logged in console output.
"""

import sys
from pathlib import Path

# Add src to path
current_dir = Path(__file__).parent
src_dir = current_dir / "backend" / "src"
sys.path.insert(0, str(src_dir))

from core.utils.logging import setup_logging
import logging

# Setup logging
setup_logging(log_filename="test.log")

logger = logging.getLogger(__name__)

# Test logging with extra fields
logger.info("Test message with extra fields", extra={
    'task_id': 'test-123',
    'task_name': 'test_task',
    'status': 'test',
    'args_count': 2
})

print("Test completed. Check console output above and test.log file.")