"""create_credit_transactions_table

Revision ID: 178467a94759
Revises: 2440f2a2652d
Create Date: 2025-09-16 00:08:13.538224

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '178467a94759'
down_revision: Union[str, Sequence[str], None] = '2440f2a2652d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('credit_transactions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('tenant_id', sa.Integer(), nullable=False),
    sa.Column('transaction_type', sa.Enum('SUBSCRIPTION_GRANT', 'PURCHASE', 'USAGE', 'EXPIRATION', 'ADJUSTMENT', name='credittransactiontype'), nullable=False),
    sa.Column('amount', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('balance_after', sa.DECIMAL(precision=10, scale=2), nullable=False),
    sa.Column('description', sa.String(length=500), nullable=True),
    sa.Column('stripe_payment_id', sa.String(length=255), nullable=True),
    sa.Column('resource_id', sa.String(length=255), nullable=True),
    sa.Column('resource_type', sa.String(length=50), nullable=True),
    sa.Column('expires_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('transaction_metadata', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_credit_transactions_id'), 'credit_transactions', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_credit_transactions_id'), table_name='credit_transactions')
    op.drop_table('credit_transactions')
    # ### end Alembic commands ###
